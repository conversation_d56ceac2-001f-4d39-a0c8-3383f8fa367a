@echo off
chcp 65001 >nul
echo ========================================
echo PUMP价格监控系统 - 完整验证流程
echo ========================================
echo.

echo 【步骤1】检查Maven环境...
mvn -version
if %errorlevel% neq 0 (
    echo ❌ Maven未正确安装
    echo 请参考 install-maven.md 文件安装Maven
    pause
    exit /b 1
)

echo.
echo ✅ Maven环境检查通过
echo.

echo 【步骤2】清理项目...
mvn clean
if %errorlevel% neq 0 (
    echo ❌ 项目清理失败
    pause
    exit /b 1
)

echo.
echo ✅ 项目清理完成
echo.

echo 【步骤3】编译项目...
mvn compile
if %errorlevel% neq 0 (
    echo ❌ 编译失败 - 请检查代码语法
    pause
    exit /b 1
)

echo.
echo ✅ 项目编译成功
echo.

echo 【步骤4】运行测试...
mvn test
if %errorlevel% neq 0 (
    echo ⚠️ 测试失败，但编译成功，继续验证...
) else (
    echo ✅ 测试通过
)

echo.
echo 【步骤5】检查关键文件修改...
echo.

echo 🔍 检查application.properties配置：
findstr /n "jupiter.api" src\main\resources\application.properties
echo.

echo 🔍 检查JupiterApiClient.java修改：
echo 【验证点1】买入价格提取：
findstr /n "buyPrice" src\main\java\com\pump\client\JupiterApiClient.java | findstr /n "quotedPrice"
echo.

echo 【验证点2】卖出价格提取：
findstr /n "sellPrice" src\main\java\com\pump\client\JupiterApiClient.java | findstr /n "quotedPrice"
echo.

echo 【验证点3】确认不再使用相同价格：
findstr /n "sellPrice = price" src\main\java\com\pump\client\JupiterApiClient.java
if %errorlevel% neq 0 (
    echo ✅ 确认：不再使用相同价格赋值
) else (
    echo ❌ 警告：仍存在相同价格赋值
)

echo.
echo ========================================
echo 🚀 准备启动系统验证
echo ========================================
echo.

echo 📋 启动后请观察以下要点：
echo 1. 输出格式应为：时间戳 - Gate.io价格 vs Jupiter买入价格 vs Jupiter卖出价格
echo 2. 买入价格和卖出价格应该不同
echo 3. 价格差异应该反映真实市场状况
echo 4. 系统应该每2秒更新一次价格
echo.

echo 💡 按任意键启动系统，或按 Ctrl+C 取消...
pause > nul

echo.
echo 🔄 启动PUMP价格监控系统...
echo 按 Ctrl+C 停止系统
echo.

mvn spring-boot:run 