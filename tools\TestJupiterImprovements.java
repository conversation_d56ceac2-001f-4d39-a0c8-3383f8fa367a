import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * Jupiter API改进功能综合测试
 * 验证所有实施的改进措施
 * 
 * <AUTHOR> Agent
 * @version 1.0
 * @since 2025-01-15
 */
public class TestJupiterImprovements {
    
    public static void main(String[] args) {
        System.out.println("=== Jupiter API改进功能综合测试 ===");
        System.out.println("测试时间: " + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        System.out.println();
        
        TestRunner runner = new TestRunner();
        
        // 测试1: 基本API连接
        runner.runTest("基本API连接测试", () -> {
            TestJupiterClient client = new TestJupiterClient();
            TestPriceData result = client.getPumpPrice();
            
            if (result != null && result.isValid()) {
                System.out.println("  ✅ 成功获取价格: $" + result.getPrice());
                return true;
            } else {
                System.out.println("  ❌ 价格获取失败: " + (result != null ? result.getErrorMessage() : "null"));
                return false;
            }
        });
        
        // 测试2: 重试机制
        runner.runTest("指数退避重试机制测试", () -> {
            long[] delays = {1000, 2000, 4000, 8000, 10000}; // 预期延迟
            
            for (int i = 1; i <= 5; i++) {
                long expectedDelay = calculateRetryDelay(i);
                long actualExpected = delays[i - 1];
                
                if (expectedDelay == actualExpected) {
                    System.out.println("  ✅ 第" + i + "次重试延迟正确: " + expectedDelay + "ms");
                } else {
                    System.out.println("  ❌ 第" + i + "次重试延迟错误: 期望" + actualExpected + "ms, 实际" + expectedDelay + "ms");
                    return false;
                }
            }
            return true;
        });
        
        // 测试3: 缓存机制
        runner.runTest("缓存机制测试", () -> {
            TestCache cache = new TestCache();
            TestPriceData testData = new TestPriceData(new BigDecimal("0.00001234"));
            
            // 存储和获取
            cache.put("test", testData);
            TestPriceData retrieved = cache.get("test");
            
            if (retrieved != null && retrieved.getPrice().equals(testData.getPrice())) {
                System.out.println("  ✅ 缓存存储和获取成功");
                
                // 测试过期
                try {
                    Thread.sleep(1100); // 等待过期
                    TestPriceData expired = cache.get("test");
                    if (expired == null) {
                        System.out.println("  ✅ 缓存过期机制正常");
                        
                        // 测试过期数据获取
                        TestPriceData stale = cache.getStale("test");
                        if (stale != null) {
                            System.out.println("  ✅ 过期数据获取成功");
                            return true;
                        }
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }
            
            System.out.println("  ❌ 缓存机制测试失败");
            return false;
        });
        
        // 测试4: 代理配置
        runner.runTest("代理配置测试", () -> {
            TestJupiterClient httpClient = new TestJupiterClient("HTTP");
            TestJupiterClient socksClient = new TestJupiterClient("SOCKS");
            
            TestPriceData httpResult = httpClient.getPumpPrice();
            TestPriceData socksResult = socksClient.getPumpPrice();
            
            boolean httpSuccess = httpResult != null && httpResult.isValid();
            boolean socksSuccess = socksResult != null && socksResult.isValid();
            
            System.out.println("  HTTP代理: " + (httpSuccess ? "✅ 成功" : "❌ 失败"));
            System.out.println("  SOCKS代理: " + (socksSuccess ? "✅ 成功" : "❌ 失败"));
            
            return httpSuccess || socksSuccess; // 至少一个成功
        });
        
        // 测试5: 报价功能
        runner.runTest("报价功能测试", () -> {
            TestJupiterClient client = new TestJupiterClient();
            
            BigDecimal buyQuote = client.getQuotePrice(new BigDecimal("100"), true);
            BigDecimal sellQuote = client.getQuotePrice(new BigDecimal("1000000"), false);
            
            boolean buySuccess = buyQuote != null && buyQuote.compareTo(BigDecimal.ZERO) > 0;
            boolean sellSuccess = sellQuote != null && sellQuote.compareTo(BigDecimal.ZERO) > 0;
            
            System.out.println("  买入报价: " + (buySuccess ? "✅ 成功 (" + buyQuote + ")" : "❌ 失败"));
            System.out.println("  卖出报价: " + (sellSuccess ? "✅ 成功 (" + sellQuote + ")" : "❌ 失败"));
            
            return buySuccess && sellSuccess;
        });
        
        // 测试6: 健康检查
        runner.runTest("API健康检查测试", () -> {
            TestJupiterClient client = new TestJupiterClient();
            boolean isHealthy = client.isApiHealthy();
            
            System.out.println("  健康状态: " + (isHealthy ? "✅ 正常" : "❌ 异常"));
            return true; // 健康检查可能因网络问题失败，不作为失败条件
        });
        
        // 输出测试总结
        runner.printSummary();
    }
    
    /**
     * 计算重试延迟（指数退避）
     */
    private static long calculateRetryDelay(int attempt) {
        long baseDelay = 1000;
        double multiplier = 2.0;
        long maxDelay = 10000;
        
        long delay = (long) (baseDelay * Math.pow(multiplier, attempt - 1));
        return Math.min(delay, maxDelay);
    }
    
    /**
     * 测试运行器
     */
    static class TestRunner {
        private int totalTests = 0;
        private int passedTests = 0;
        
        public void runTest(String testName, TestCase testCase) {
            totalTests++;
            System.out.println((totalTests) + ". " + testName + "...");
            
            try {
                boolean result = testCase.run();
                if (result) {
                    passedTests++;
                    System.out.println("   结果: ✅ 通过");
                } else {
                    System.out.println("   结果: ❌ 失败");
                }
            } catch (Exception e) {
                System.out.println("   结果: ❌ 异常 - " + e.getMessage());
            }
            
            System.out.println();
        }
        
        public void printSummary() {
            System.out.println("=== 测试总结 ===");
            System.out.println("总测试数: " + totalTests);
            System.out.println("通过数: " + passedTests);
            System.out.println("失败数: " + (totalTests - passedTests));
            System.out.println("通过率: " + String.format("%.1f%%", (double) passedTests / totalTests * 100));
            
            if (passedTests == totalTests) {
                System.out.println("🎉 所有测试通过！Jupiter API改进实施成功！");
            } else {
                System.out.println("⚠️ 部分测试失败，需要进一步调试");
            }
        }
    }
    
    /**
     * 测试用例接口
     */
    @FunctionalInterface
    interface TestCase {
        boolean run() throws Exception;
    }
    
    /**
     * 简化的Jupiter客户端（用于测试）
     */
    static class TestJupiterClient {
        private final String proxyType;
        
        public TestJupiterClient() {
            this("HTTP");
        }
        
        public TestJupiterClient(String proxyType) {
            this.proxyType = proxyType;
        }
        
        public TestPriceData getPumpPrice() {
            try {
                String url = "https://lite-api.jup.ag/price/v3?ids=83cXjb9Z8iJSBK2DBacte1utUsygdotMBQVhJoSppump";
                java.net.HttpURLConnection connection = createConnection(url);
                
                int responseCode = connection.getResponseCode();
                if (responseCode == 200) {
                    String response = readResponse(connection);
                    return parseResponse(response);
                }
            } catch (Exception e) {
                return new TestPriceData("连接失败: " + e.getMessage());
            }
            return new TestPriceData("API调用失败");
        }
        
        public BigDecimal getQuotePrice(BigDecimal amount, boolean isBuy) {
            try {
                String inputMint = isBuy ? "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v" : "83cXjb9Z8iJSBK2DBacte1utUsygdotMBQVhJoSppump";
                String outputMint = isBuy ? "83cXjb9Z8iJSBK2DBacte1utUsygdotMBQVhJoSppump" : "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v";
                
                BigDecimal amountInSmallestUnit = amount.multiply(new BigDecimal("1000000"));
                String url = String.format("https://lite-api.jup.ag/swap/v1/quote?inputMint=%s&outputMint=%s&amount=%s&slippageBps=50",
                                         inputMint, outputMint, amountInSmallestUnit.toBigInteger());
                
                java.net.HttpURLConnection connection = createConnection(url);
                int responseCode = connection.getResponseCode();
                
                if (responseCode == 200) {
                    String response = readResponse(connection);
                    return parseQuoteResponse(response);
                }
            } catch (Exception e) {
                // 忽略异常
            }
            return null;
        }
        
        public boolean isApiHealthy() {
            try {
                java.net.HttpURLConnection connection = createConnection("https://lite-api.jup.ag/health");
                return connection.getResponseCode() == 200;
            } catch (Exception e) {
                return false;
            }
        }
        
        private java.net.HttpURLConnection createConnection(String urlString) throws Exception {
            java.net.URL url = new java.net.URL(urlString);
            java.net.HttpURLConnection connection;
            
            // 使用代理
            java.net.Proxy.Type type = "SOCKS".equals(proxyType) ? 
                java.net.Proxy.Type.SOCKS : java.net.Proxy.Type.HTTP;
            java.net.Proxy proxy = new java.net.Proxy(type, 
                new java.net.InetSocketAddress("127.0.0.1", 7890));
            
            connection = (java.net.HttpURLConnection) url.openConnection(proxy);
            connection.setRequestMethod("GET");
            connection.setRequestProperty("User-Agent", "PUMP-Monitor/1.0");
            connection.setConnectTimeout(15000);
            connection.setReadTimeout(15000);
            
            return connection;
        }
        
        private String readResponse(java.net.HttpURLConnection connection) throws Exception {
            java.io.BufferedReader reader = new java.io.BufferedReader(
                new java.io.InputStreamReader(connection.getInputStream()));
            StringBuilder response = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                response.append(line);
            }
            reader.close();
            return response.toString();
        }
        
        private TestPriceData parseResponse(String response) {
            try {
                if (response.contains("usdPrice")) {
                    String priceStr = extractPrice(response);
                    if (priceStr != null) {
                        return new TestPriceData(new BigDecimal(priceStr));
                    }
                }
            } catch (Exception e) {
                // 忽略解析异常
            }
            return new TestPriceData("解析失败");
        }
        
        private BigDecimal parseQuoteResponse(String response) {
            try {
                String searchKey = "\"outAmount\":\"";
                int startIndex = response.indexOf(searchKey);
                if (startIndex != -1) {
                    startIndex += searchKey.length();
                    int endIndex = response.indexOf("\"", startIndex);
                    if (endIndex != -1) {
                        String amountStr = response.substring(startIndex, endIndex);
                        BigDecimal outAmount = new BigDecimal(amountStr);
                        return outAmount.divide(new BigDecimal("1000000"));
                    }
                }
            } catch (Exception e) {
                // 忽略解析异常
            }
            return null;
        }
        
        private String extractPrice(String json) {
            try {
                String searchKey = "\"usdPrice\":";
                int startIndex = json.indexOf(searchKey);
                if (startIndex != -1) {
                    startIndex += searchKey.length();
                    int endIndex = json.indexOf(",", startIndex);
                    if (endIndex == -1) endIndex = json.indexOf("}", startIndex);
                    if (endIndex != -1) {
                        return json.substring(startIndex, endIndex).trim();
                    }
                }
            } catch (Exception e) {
                // 忽略异常
            }
            return null;
        }
    }
    
    /**
     * 简化的价格数据类
     */
    static class TestPriceData {
        private BigDecimal price;
        private String errorMessage;
        
        public TestPriceData(BigDecimal price) {
            this.price = price;
        }
        
        public TestPriceData(String errorMessage) {
            this.errorMessage = errorMessage;
        }
        
        public boolean isValid() {
            return price != null && price.compareTo(BigDecimal.ZERO) > 0;
        }
        
        public BigDecimal getPrice() { return price; }
        public String getErrorMessage() { return errorMessage; }
    }
    
    /**
     * 简化的缓存实现
     */
    static class TestCache {
        private final java.util.Map<String, TestCacheEntry> cache = new java.util.HashMap<>();
        private static final long TTL = 1000; // 1秒TTL
        
        public void put(String key, TestPriceData data) {
            cache.put(key, new TestCacheEntry(data, System.currentTimeMillis()));
        }
        
        public TestPriceData get(String key) {
            TestCacheEntry entry = cache.get(key);
            if (entry != null && (System.currentTimeMillis() - entry.timestamp) <= TTL) {
                return entry.data;
            }
            return null;
        }
        
        public TestPriceData getStale(String key) {
            TestCacheEntry entry = cache.get(key);
            return entry != null ? entry.data : null;
        }
        
        static class TestCacheEntry {
            final TestPriceData data;
            final long timestamp;
            
            TestCacheEntry(TestPriceData data, long timestamp) {
                this.data = data;
                this.timestamp = timestamp;
            }
        }
    }
}
