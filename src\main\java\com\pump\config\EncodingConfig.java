package com.pump.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Bean;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.web.filter.CharacterEncodingFilter;

import javax.annotation.PostConstruct;
import java.io.PrintStream;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;

/**
 * 编码配置类
 * 确保整个应用使用UTF-8编码
 * 
 * <AUTHOR> Agent
 * @version 1.0
 * @since 2025-01-15
 */
@Configuration
public class EncodingConfig {
    
    /**
     * 应用启动后配置编码
     */
    @PostConstruct
    public void configureEncoding() {
        try {
            // 重新设置System.out和System.err为UTF-8编码
            System.setOut(new PrintStream(System.out, true, StandardCharsets.UTF_8.name()));
            System.setErr(new PrintStream(System.err, true, StandardCharsets.UTF_8.name()));
            
            // 设置默认字符集
            System.setProperty("file.encoding", "UTF-8");
            System.setProperty("sun.jnu.encoding", "UTF-8");
            
        } catch (UnsupportedEncodingException e) {
            System.err.println("配置UTF-8编码失败: " + e.getMessage());
        }
    }
    
    /**
     * 配置字符编码过滤器
     * 确保Web请求使用UTF-8编码
     */
    @Bean
    public FilterRegistrationBean<CharacterEncodingFilter> characterEncodingFilter() {
        FilterRegistrationBean<CharacterEncodingFilter> registrationBean = new FilterRegistrationBean<>();
        CharacterEncodingFilter characterEncodingFilter = new CharacterEncodingFilter();
        
        characterEncodingFilter.setEncoding("UTF-8");
        characterEncodingFilter.setForceEncoding(true);
        
        registrationBean.setFilter(characterEncodingFilter);
        registrationBean.addUrlPatterns("/*");
        registrationBean.setOrder(1);
        
        return registrationBean;
    }
}
