package com.pump.scheduler;


import com.pump.service.PumpPriceService;
import com.pump.service.AlertSoundService;
import com.pump.config.PumpConfigService;
import com.pump.util.ConsoleEncodingUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import javax.annotation.PostConstruct;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 价格监控定时任务调度器
 * 定期执行价格监控和套利分析
 * 
 * <AUTHOR> Agent
 * @version 1.0
 * @since 2025-01-15
 */
@Component
public class PriceMonitorScheduler {
    
    private static final Logger logger = LoggerFactory.getLogger(PriceMonitorScheduler.class);

    // 时间格式化器
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
    
    @Autowired
    private PumpPriceService pumpPriceService;

    @Autowired
    private AlertSoundService alertSoundService;

    @Autowired
    private PumpConfigService configService;
    
    /** 日期时间格式化器 */
    private final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    /** 任务运行计数器 */
    private long taskCounter = 0;
    
    /** 系统启动时间 */
    private final LocalDateTime startTime = LocalDateTime.now();

    /** 上次执行时间（用于动态间隔控制） */
    private long lastExecutionTime = 0;
    
    /**
     * 定时执行价格监控任务
     * 每${pump.monitor.interval}毫秒执行一次
     */
    @Scheduled(fixedDelay = 1000) // 使用1秒作为基础间隔，内部控制实际间隔
    public void monitorPrices() {
        // 检查是否到了执行时间
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastExecutionTime < configService.getMonitorInterval()) {
            return; // 还没到执行时间
        }
        lastExecutionTime = currentTime;
        taskCounter++;

        // 添加调试信息确认调度器正在运行
        if (taskCounter % 5 == 1) { // 每5次输出一次，避免日志过多
            logger.info("执行价格监控任务 #{}", taskCounter);
        }

        try {
            // 简化输出：只显示三个关键价格
            outputSimplifiedPrices();

        } catch (Exception e) {
            logger.error("价格监控任务执行失败: {}", e.getMessage(), e);
            // 输出详细的堆栈跟踪以便调试
            e.printStackTrace();
        }
    }

    /**
     * 输出格式化的价格监控信息
     * 按照新的目标格式显示价格和差价信息
     */
    private void outputSimplifiedPrices() {
        try {
            BigDecimal amount = new BigDecimal("1000000"); // 100万个PUMP
            String timestamp = LocalDateTime.now().format(TIME_FORMATTER);

            // 获取Gate.io订单簿价格（100万个PUMP的总价）
            com.pump.model.PriceData cexPrice = pumpPriceService.getCexPrice();
            BigDecimal gateTotalPrice = null;
            BigDecimal gateUnitPrice = null;
            if (cexPrice != null && cexPrice.isValid()) {
                gateUnitPrice = cexPrice.getLastPrice();
                gateTotalPrice = gateUnitPrice.multiply(amount);
            }

            // 获取Jupiter Ultra API买入价格（100万个PUMP的总USDT成本）
            BigDecimal jupiterBuyTotalPrice = pumpPriceService.getBuyPrice(amount, "DEX");

            // 获取Jupiter Ultra API卖出价格（100万个PUMP的总USDT收入）
            BigDecimal jupiterSellTotalPrice = pumpPriceService.getSellPrice(amount, "DEX");

            // 按照目标格式输出
            if (gateTotalPrice != null) {
                // 第一行：分隔线标题 + GATE总价（100万个PUMP）
                logger.info("{} : ===============PUMP监控 ===============GATE单价: ${}",
                    timestamp, String.format("%.2f", gateTotalPrice));

                // 第二行：池买入价格 + 差价 + 做升
                if (jupiterBuyTotalPrice != null) {
                    // 修正：买入差价 = Gate.io总价 - Ultra API买入总价
                    BigDecimal buyDifference = gateTotalPrice.subtract(jupiterBuyTotalPrice);
                    logger.info("{} : 池买入100W个PUMP: ${}，差价：${}，做升",
                        timestamp,
                        String.format("%.2f", jupiterBuyTotalPrice),
                        String.format("%.2f", buyDifference));

                    // 检查买入报警
                    alertSoundService.checkBuyAlert(buyDifference, jupiterBuyTotalPrice, gateTotalPrice);
                } else {
                    logger.info("{} : 池买入100W个PUMP: 获取失败，差价：--，做升", timestamp);
                }

                // 第三行：池卖出价格 + 差价 + 做跌
                if (jupiterSellTotalPrice != null) {
                    // 保持：卖出差价 = Ultra API卖出总价 - Gate.io总价
                    BigDecimal sellDifference = jupiterSellTotalPrice.subtract(gateTotalPrice);
                    logger.info("{} : 池卖出100W个PUMP: ${}，差价：${}，做跌",
                        timestamp,
                        String.format("%.2f", jupiterSellTotalPrice),
                        String.format("%.2f", sellDifference));

                    // 检查卖出报警
                    alertSoundService.checkSellAlert(sellDifference, jupiterSellTotalPrice, gateTotalPrice);
                } else {
                    logger.info("{} : 池卖出100W个PUMP: 获取失败，差价：--，做跌", timestamp);
                }
            } else {
                // Gate.io价格获取失败的情况
                logger.info("{} : ===============PUMP监控 ===============GATE单价: 获取失败", timestamp);
                logger.info("{} : 池买入100W个PUMP: {}，差价：--，做升",
                    timestamp,
                    jupiterBuyTotalPrice != null ? String.format("$%.2f", jupiterBuyTotalPrice) : "获取失败");
                logger.info("{} : 池卖出100W个PUMP: {}，差价：--，做跌",
                    timestamp,
                    jupiterSellTotalPrice != null ? String.format("$%.2f", jupiterSellTotalPrice) : "获取失败");
            }

        } catch (Exception e) {
            logger.error("获取价格信息失败: {}", e.getMessage());
        }
    }

    /**
     * 添加详细的调试输出
     * 显示CEX和DEX的原始价格数据
     */
    private void addDetailedDebugOutput() {
        try {
            // 获取100万个PUMP的价格数据
            BigDecimal amount = new BigDecimal("1000000");

            // 获取CEX价格
            BigDecimal cexBuyPrice = pumpPriceService.getBuyPrice(amount, "CEX");
            BigDecimal cexSellPrice = pumpPriceService.getSellPrice(amount, "CEX");

            // 获取DEX价格
            BigDecimal dexBuyPrice = pumpPriceService.getBuyPrice(amount, "DEX");
            BigDecimal dexSellPrice = pumpPriceService.getSellPrice(amount, "DEX");

            // 输出调试信息
            logger.debug("=== 价格调试信息 (100万个PUMP) ===");
            logger.debug("CEX买入价格: {} USDT", cexBuyPrice != null ? cexBuyPrice : "获取失败");
            logger.debug("CEX卖出价格: {} USDT", cexSellPrice != null ? cexSellPrice : "获取失败");
            logger.debug("DEX买入价格: {} USDT", dexBuyPrice != null ? dexBuyPrice : "获取失败");
            logger.debug("DEX卖出价格: {} USDT", dexSellPrice != null ? dexSellPrice : "获取失败");
            logger.debug("================================");

        } catch (Exception e) {
            logger.debug("获取调试价格信息失败: {}", e.getMessage());
        }
    }
    
    /**
     * 输出系统运行统计信息
     */
    private void logStatistics() {
        LocalDateTime now = LocalDateTime.now();
        long runningMinutes = java.time.Duration.between(startTime, now).toMinutes();
        
        logger.info("=== 系统运行统计 ===");
        logger.info("启动时间: {}", startTime.format(dateTimeFormatter));
        logger.info("当前时间: {}", now.format(dateTimeFormatter));
        logger.info("运行时间: {} 分钟", runningMinutes);
        logger.info("执行任务数: {}", taskCounter);
        logger.info("平均执行间隔: {} 毫秒", configService.getMonitorInterval());
        logger.info("==================");
    }
    
    /**
     * 启动时输出系统信息 - 简化版本
     * 使用PostConstruct确保只执行一次
     */
    @PostConstruct
    public void printStartupInfo() {
        // 初始化控制台编码
        ConsoleEncodingUtil.initializeConsoleEncoding();

        // 延迟2秒后输出启动信息，确保其他组件已初始化
        new Thread(() -> {
            try {
                Thread.sleep(2000);
                logger.info("PUMP价格监控系统已启动，监控间隔: {}ms", configService.getMonitorInterval());
                logger.info("{}", alertSoundService.getConfigInfo());
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }).start();
    }
    
    /**
     * Java 8兼容的字符串重复方法
     * 
     * @param str 要重复的字符串
     * @param count 重复次数
     * @return 重复后的字符串
     */
    private String repeatString(String str, int count) {
        if (count <= 0) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < count; i++) {
            sb.append(str);
        }
        return sb.toString();
    }
    
    /**
     * 获取运行统计信息
     * 
     * @return 统计信息字符串
     */
    public String getStatistics() {
        LocalDateTime now = LocalDateTime.now();
        long runningMinutes = java.time.Duration.between(startTime, now).toMinutes();
        
        StringBuilder sb = new StringBuilder();
        sb.append("=== 系统运行统计 ===\n");
        sb.append("启动时间: ").append(startTime.format(dateTimeFormatter)).append("\n");
        sb.append("当前时间: ").append(now.format(dateTimeFormatter)).append("\n");
        sb.append("运行时间: ").append(runningMinutes).append(" 分钟\n");
        sb.append("执行任务数: ").append(taskCounter).append("\n");
        sb.append("平均执行间隔: ").append(configService.getMonitorInterval()).append(" 毫秒\n");
        sb.append("==================");
        
        return sb.toString();
    }
    
    /**
     * 重置统计计数器
     */
    public void resetStatistics() {
        taskCounter = 0;
        logger.info("统计计数器已重置");
    }
    
    /**
     * 获取任务计数器
     * 
     * @return 任务计数器
     */
    public long getTaskCounter() {
        return taskCounter;
    }
    
    /**
     * 获取系统启动时间
     * 
     * @return 启动时间
     */
    public LocalDateTime getStartTime() {
        return startTime;
    }
} 