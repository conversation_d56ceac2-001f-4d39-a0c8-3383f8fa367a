# 📚 PUMP价格监控系统文档

本目录包含PUMP价格监控系统的所有文档文件。

## 📖 用户指南

### 核心配置文档
- [JSON配置使用说明.md](JSON配置使用说明.md) - 系统配置详细说明
- [自定义音频使用指南.md](自定义音频使用指南.md) - 音频文件配置指南
- [API限制和报警优化说明.md](API限制和报警优化说明.md) - API使用限制和优化

### 问题排查
- [报警问题诊断修复.md](报警问题诊断修复.md) - 报警功能故障排除

## 🔧 技术文档

### API相关
- [Jupiter_API_对比分析.md](Jupiter_API_对比分析.md) - Jupiter API分析
- [Jupiter_Quote_API_修复报告.md](Jupiter_Quote_API_修复报告.md) - Quote API修复

### 系统修复报告
- [PUMP价格监控系统优化修复报告.md](PUMP价格监控系统优化修复报告.md)
- [PUMP价格监控系统问题修复报告.md](PUMP价格监控系统问题修复报告.md)
- [PUMP价格计算问题修复报告.md](PUMP价格计算问题修复报告.md)

### 价格逻辑修复
- [买入卖出价差修复报告.md](买入卖出价差修复报告.md)
- [买入卖出逻辑修复报告.md](买入卖出逻辑修复报告.md)
- [6000美元买入价格修复报告.md](6000美元买入价格修复报告.md)

## 🛠️ 开发文档

### 环境配置
- [complete-env-setup.md](complete-env-setup.md) - 完整环境配置
- [install-maven.md](install-maven.md) - Maven安装指南
- [jar-build-guide.md](jar-build-guide.md) - JAR构建指南

### 项目状态
- [PROJECT-STATUS.md](PROJECT-STATUS.md) - 项目状态总览
- [verification-report.md](verification-report.md) - 验证报告
- [修改总结.md](修改总结.md) - 修改总结

## 📋 其他文档

### MVP版本
- [README-MVP.md](README-MVP.md) - MVP版本说明

### 输出优化
- [简化价格输出修复说明.md](简化价格输出修复说明.md) - 输出格式优化

## 📁 文档分类

```
docs/
├── 用户指南/           # 用户使用相关
├── 技术文档/           # 开发技术相关  
├── 修复报告/           # 问题修复记录
├── 环境配置/           # 开发环境配置
└── 历史文档/           # 历史版本文档
```

## 🔍 快速查找

### 我想要...
- **配置系统** → [JSON配置使用说明.md](JSON配置使用说明.md)
- **添加音频** → [自定义音频使用指南.md](自定义音频使用指南.md)
- **解决报警问题** → [报警问题诊断修复.md](报警问题诊断修复.md)
- **了解API限制** → [API限制和报警优化说明.md](API限制和报警优化说明.md)
- **搭建环境** → [complete-env-setup.md](complete-env-setup.md)
- **构建JAR** → [jar-build-guide.md](jar-build-guide.md)

---

💡 **提示**: 所有文档都使用Markdown格式编写，可以在任何支持Markdown的编辑器中查看。
