# PUMP代币价格监控系统 - 技术文档总览

## 📚 文档导航

本技术文档集为PUMP代币价格监控系统提供全面的技术参考，涵盖系统架构、实现逻辑和测试验证等核心内容。

### 📖 文档结构

| 文档名称 | 描述 | 适用对象 |
|---------|------|----------|
| [系统架构文档](architecture.md) | 系统整体架构、组件设计、技术栈 | 架构师、技术负责人 |
| [实现逻辑详解](implementation-logic.md) | 核心业务逻辑、算法实现、技术细节 | 开发工程师、维护人员 |
| [测试验证策略](testing-strategy.md) | 测试计划、验证方案、质量保证 | 测试工程师、QA团队 |

---

## 🎯 系统概述

### 核心功能
PUMP代币价格监控系统是一个基于Spring Boot的实时价格监控和套利分析系统，专门监控PUMP代币在中心化交易所(Gate.io)和去中心化交易所(Jupiter DEX)之间的价格差异。

### 关键特性
- **实时监控**: 每2秒获取100万PUMP代币的CEX和DEX价格
- **智能分析**: 自动计算买入/卖出价格差异，识别套利机会
- **音频告警**: 基于阈值的音频告警系统，支持自定义音频文件
- **配置管理**: JSON配置内嵌到JAR，支持运行时参数调整
- **缓存优化**: 价格数据缓存机制，减少API调用频率

### 技术亮点
- **UTF-8编码处理**: 完善的中文字符支持
- **容错机制**: 指数退避重试、降级处理
- **性能优化**: 缓存机制、异步处理
- **监控告警**: 多种音频告警模式

---

## 🏗️ 架构概览

### 系统架构图
```mermaid
graph TB
    subgraph "核心组件"
        A[PumpApplication<br/>主程序] --> B[PriceMonitorScheduler<br/>定时调度器]
        B --> C[PumpPriceService<br/>价格服务]
        C --> D[GateIoApiClient<br/>CEX客户端]
        C --> E[JupiterApiClient<br/>DEX客户端]
        B --> F[AlertSoundService<br/>告警服务]
    end
    
    subgraph "外部依赖"
        G[Gate.io API]
        H[Jupiter Quote API]
        I[Jupiter Price API]
    end
    
    subgraph "配置资源"
        J[pump-config.json]
        K[up.wav / down.wav]
    end
    
    D --> G
    E --> H
    E --> I
    F --> K
    
    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style F fill:#fff3e0
```

### 技术栈
- **框架**: Spring Boot 2.7.0
- **构建工具**: Maven 3.8+
- **HTTP客户端**: RestTemplate, HttpURLConnection
- **JSON处理**: Jackson ObjectMapper
- **日志系统**: SLF4J + Logback
- **音频处理**: Java Sound API

---

## 💰 核心业务逻辑

### 价格监控流程
1. **定时触发**: 每2秒执行一次价格监控任务
2. **价格获取**: 并行获取Gate.io和Jupiter的价格数据
3. **差价计算**: 计算买入/卖出价格差异
4. **告警判断**: 基于阈值判断是否触发音频告警
5. **结果输出**: 格式化输出价格信息和交易建议

### 差价计算公式
```
买入差价 = Gate.io总价 - Jupiter买入总价
卖出差价 = Jupiter卖出总价 - Gate.io总价
```

### 告警触发条件
- **买入告警**: 买入差价 > $30 且未在播放中
- **卖出告警**: 卖出差价 > $30 且未在播放中
- **音频文件**: up.wav (买入), down.wav (卖出)

---

## 🧪 测试验证体系

### 测试层次
1. **单元测试**: 各组件独立功能验证
2. **集成测试**: 组件间协作验证
3. **性能测试**: 系统性能指标验证
4. **错误场景测试**: 异常处理能力验证

### 测试工具集
项目提供了丰富的测试工具，位于`tools/`目录：

| 工具名称 | 功能描述 | 使用场景 |
|---------|----------|----------|
| TestAlertSound.java | 音频告警系统测试 | 验证音频播放功能 |
| TestBuySellPriceDifference.java | 价格差异计算测试 | 验证差价算法 |
| TestJupiterQuoteAPIFix.java | Jupiter API测试 | 验证API集成 |
| TestCurrentLogic.java | 当前逻辑验证 | 端到端功能测试 |

### 性能基准
- **API响应时间**: < 2秒 (平均)
- **内存使用**: < 512MB
- **监控频率**: 2秒/次 (可配置)
- **成功率**: > 95%

---

## 📊 关键配置参数

### 监控配置
```json
{
  "monitor": {
    "interval": 2000,        // 监控间隔(毫秒)
    "amount": 1000000        // 监控数量(PUMP代币)
  }
}
```

### 告警配置
```json
{
  "alert": {
    "enabled": true,         // 是否启用告警
    "buyThreshold": 30.00,   // 买入告警阈值(美元)
    "sellThreshold": 30.00,  // 卖出告警阈值(美元)
    "soundType": "CUSTOM"    // 音频类型
  }
}
```

### API配置
```json
{
  "api": {
    "timeout": 30000,        // API超时时间(毫秒)
    "retryDelay": 2000       // 重试延迟(毫秒)
  }
}
```

---

## 🚀 快速开始

### 环境要求
- **Java**: JDK 11+
- **Maven**: 3.8+
- **网络**: 能访问Gate.io和Jupiter API
- **音频**: 支持WAV格式播放

### 构建运行
```bash
# 1. 编译项目
mvn clean compile

# 2. 打包JAR
mvn package

# 3. 运行系统
java -jar pump30.jar

# 或使用启动脚本
./Start-Pump30.ps1  # Windows PowerShell
./pump.bat          # Windows批处理
```

### 验证安装
```bash
# 运行基础测试
cd tools
java -cp ".:../target/classes:../target/lib/*" TestCurrentLogic

# 运行音频测试
java -cp ".:../target/classes:../target/lib/*" TestAlertSound
```

---

## 🔧 运维监控

### 日志管理
- **日志级别**: INFO (价格信息), WARN (告警), ERROR (错误)
- **日志格式**: 时间戳 + 消息内容
- **编码设置**: UTF-8确保中文正确显示

### 性能监控
- **内存使用**: 通过JVM参数监控
- **API响应**: 日志记录响应时间
- **成功率**: 统计API调用成功率

### 故障处理
1. **API异常**: 自动重试机制
2. **网络问题**: 降级到缓存数据
3. **音频故障**: 静默处理，不影响主流程
4. **配置错误**: 启动时快速失败

---

## 📈 扩展指南

### 功能扩展点
- **新增交易所**: 实现新的API客户端
- **新增告警方式**: 扩展AlertSoundService
- **新增分析算法**: 扩展套利分析逻辑
- **新增存储方式**: 添加数据持久化

### 配置扩展
- **多交易所支持**: 配置多个价格源
- **多代币支持**: 扩展到其他代币监控
- **多告警渠道**: 邮件、短信、Webhook等

---

## 📞 技术支持

### 常见问题
1. **中文乱码**: 检查UTF-8编码设置
2. **音频无声**: 验证音频设备和文件
3. **API超时**: 检查网络连接和代理设置
4. **价格异常**: 验证API响应格式

### 调试工具
- **健康检查**: DiagnosticTool.performHealthCheck()
- **日志分析**: LogAnalyzer.analyzeSystemLogs()
- **性能测试**: performance-benchmark.sh

### 联系方式
- **技术文档**: 查看docs/目录下的详细文档
- **测试工具**: 使用tools/目录下的测试程序
- **配置参考**: 参考pump-config.json配置说明

---

## 📝 版本信息

- **当前版本**: v1.0 MVP
- **最后更新**: 2025-01-16
- **维护状态**: 活跃开发中
- **兼容性**: Java 11+, Spring Boot 2.7+

---

## 📋 文档更新日志

| 日期 | 版本 | 更新内容 |
|------|------|----------|
| 2025-01-16 | v1.0 | 初始版本，包含完整的技术文档 |

---

**技术文档维护者**: AI Agent  
**项目状态**: MVP已完成，持续优化中
