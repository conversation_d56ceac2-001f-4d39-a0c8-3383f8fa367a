@echo off
chcp 65001 >nul
echo ========================================
echo PUMP价格监控系统 - JAR运行脚本
echo ========================================
echo.

echo 检查Java环境...
java -version > nul 2>&1
if %errorlevel% neq 0 (
    echo Java未安装，请先安装Java JDK/JRE
    pause
    exit /b 1
)

echo Java环境检查通过
echo.

echo 检查JAR文件...
if not exist "target\pump-price-monitor-1.0-SNAPSHOT.jar" (
    echo JAR文件不存在，请先运行: .\build-jar.bat
    pause
    exit /b 1
)

echo JAR文件检查通过
echo.

echo ========================================
echo 启动PUMP价格监控系统...
echo ========================================
echo.

echo 系统启动中，请等待...
echo 按 Ctrl+C 停止系统
echo.

java -jar target\pump-price-monitor-1.0-SNAPSHOT.jar 