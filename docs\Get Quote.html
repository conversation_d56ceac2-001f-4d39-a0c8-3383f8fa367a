<!DOCTYPE html>
<!-- saved from url=(0042)https://dev.jup.ag/docs/swap-api/get-quote -->
<html lang="en" dir="ltr" class="docs-wrapper plugin-docs plugin-id-docs docs-version-current docs-doc-page docs-doc-id-swap-api/get-quote" data-has-hydrated="true" data-theme="light" data-announcement-bar-initially-dismissed="false" data-rh="lang,dir,class,data-has-hydrated"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">

<meta name="generator" content="Docusaurus v3.7.0">
<title>Get Quote</title><meta data-rh="true" property="og:locale" content="en"><meta data-rh="true" name="docusaurus_locale" content="en"><meta data-rh="true" name="docsearch:language" content="en"><meta data-rh="true" property="description" content="Jupiter Developer Docs provides comprehensive documentation, tool kits and references for developing with the Jupiter API."><meta data-rh="true" property="og:image" content="img/dev-meta.png"><meta data-rh="true" name="theme-color" content="#000000"><meta data-rh="true" name="docusaurus_version" content="current"><meta data-rh="true" name="docusaurus_tag" content="docs-docs-current"><meta data-rh="true" name="docsearch:version" content="current"><meta data-rh="true" name="docsearch:docusaurus_tag" content="docs-docs-current"><link data-rh="true" rel="icon" href="https://dev.jup.ag/img/favicon.ico"><link data-rh="true" rel="preconnect" href="https://d6vf00eq1a-dsn.algolia.net/" crossorigin="anonymous">

<style type="text/css">@font-face {font-family:Inter;font-style:normal;font-weight:400;src:url(/cf-fonts/v/inter/5.0.16/cyrillic/wght/normal.woff2);unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116;font-display:swap;}@font-face {font-family:Inter;font-style:normal;font-weight:400;src:url(/cf-fonts/v/inter/5.0.16/latin-ext/wght/normal.woff2);unicode-range:U+0100-02AF,U+0304,U+0308,U+0329,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20CF,U+2113,U+2C60-2C7F,U+A720-A7FF;font-display:swap;}@font-face {font-family:Inter;font-style:normal;font-weight:400;src:url(/cf-fonts/v/inter/5.0.16/vietnamese/wght/normal.woff2);unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB;font-display:swap;}@font-face {font-family:Inter;font-style:normal;font-weight:400;src:url(/cf-fonts/v/inter/5.0.16/greek-ext/wght/normal.woff2);unicode-range:U+1F00-1FFF;font-display:swap;}@font-face {font-family:Inter;font-style:normal;font-weight:400;src:url(/cf-fonts/v/inter/5.0.16/latin/wght/normal.woff2);unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+2074,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD;font-display:swap;}@font-face {font-family:Inter;font-style:normal;font-weight:400;src:url(/cf-fonts/v/inter/5.0.16/greek/wght/normal.woff2);unicode-range:U+0370-03FF;font-display:swap;}@font-face {font-family:Inter;font-style:normal;font-weight:400;src:url(/cf-fonts/v/inter/5.0.16/cyrillic-ext/wght/normal.woff2);unicode-range:U+0460-052F,U+1C80-1C88,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F;font-display:swap;}@font-face {font-family:Inter;font-style:normal;font-weight:500;src:url(/cf-fonts/v/inter/5.0.16/greek/wght/normal.woff2);unicode-range:U+0370-03FF;font-display:swap;}@font-face {font-family:Inter;font-style:normal;font-weight:500;src:url(/cf-fonts/v/inter/5.0.16/cyrillic/wght/normal.woff2);unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116;font-display:swap;}@font-face {font-family:Inter;font-style:normal;font-weight:500;src:url(/cf-fonts/v/inter/5.0.16/cyrillic-ext/wght/normal.woff2);unicode-range:U+0460-052F,U+1C80-1C88,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F;font-display:swap;}@font-face {font-family:Inter;font-style:normal;font-weight:500;src:url(/cf-fonts/v/inter/5.0.16/latin/wght/normal.woff2);unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+2074,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD;font-display:swap;}@font-face {font-family:Inter;font-style:normal;font-weight:500;src:url(/cf-fonts/v/inter/5.0.16/latin-ext/wght/normal.woff2);unicode-range:U+0100-02AF,U+0304,U+0308,U+0329,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20CF,U+2113,U+2C60-2C7F,U+A720-A7FF;font-display:swap;}@font-face {font-family:Inter;font-style:normal;font-weight:500;src:url(/cf-fonts/v/inter/5.0.16/vietnamese/wght/normal.woff2);unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB;font-display:swap;}@font-face {font-family:Inter;font-style:normal;font-weight:500;src:url(/cf-fonts/v/inter/5.0.16/greek-ext/wght/normal.woff2);unicode-range:U+1F00-1FFF;font-display:swap;}@font-face {font-family:Inter;font-style:normal;font-weight:600;src:url(/cf-fonts/v/inter/5.0.16/greek/wght/normal.woff2);unicode-range:U+0370-03FF;font-display:swap;}@font-face {font-family:Inter;font-style:normal;font-weight:600;src:url(/cf-fonts/v/inter/5.0.16/cyrillic/wght/normal.woff2);unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116;font-display:swap;}@font-face {font-family:Inter;font-style:normal;font-weight:600;src:url(/cf-fonts/v/inter/5.0.16/cyrillic-ext/wght/normal.woff2);unicode-range:U+0460-052F,U+1C80-1C88,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F;font-display:swap;}@font-face {font-family:Inter;font-style:normal;font-weight:600;src:url(/cf-fonts/v/inter/5.0.16/greek-ext/wght/normal.woff2);unicode-range:U+1F00-1FFF;font-display:swap;}@font-face {font-family:Inter;font-style:normal;font-weight:600;src:url(/cf-fonts/v/inter/5.0.16/latin/wght/normal.woff2);unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+2074,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD;font-display:swap;}@font-face {font-family:Inter;font-style:normal;font-weight:600;src:url(/cf-fonts/v/inter/5.0.16/latin-ext/wght/normal.woff2);unicode-range:U+0100-02AF,U+0304,U+0308,U+0329,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20CF,U+2113,U+2C60-2C7F,U+A720-A7FF;font-display:swap;}@font-face {font-family:Inter;font-style:normal;font-weight:600;src:url(/cf-fonts/v/inter/5.0.16/vietnamese/wght/normal.woff2);unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB;font-display:swap;}@font-face {font-family:Inter;font-style:normal;font-weight:700;src:url(/cf-fonts/v/inter/5.0.16/greek-ext/wght/normal.woff2);unicode-range:U+1F00-1FFF;font-display:swap;}@font-face {font-family:Inter;font-style:normal;font-weight:700;src:url(/cf-fonts/v/inter/5.0.16/greek/wght/normal.woff2);unicode-range:U+0370-03FF;font-display:swap;}@font-face {font-family:Inter;font-style:normal;font-weight:700;src:url(/cf-fonts/v/inter/5.0.16/vietnamese/wght/normal.woff2);unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB;font-display:swap;}@font-face {font-family:Inter;font-style:normal;font-weight:700;src:url(/cf-fonts/v/inter/5.0.16/latin-ext/wght/normal.woff2);unicode-range:U+0100-02AF,U+0304,U+0308,U+0329,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20CF,U+2113,U+2C60-2C7F,U+A720-A7FF;font-display:swap;}@font-face {font-family:Inter;font-style:normal;font-weight:700;src:url(/cf-fonts/v/inter/5.0.16/cyrillic/wght/normal.woff2);unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116;font-display:swap;}@font-face {font-family:Inter;font-style:normal;font-weight:700;src:url(/cf-fonts/v/inter/5.0.16/cyrillic-ext/wght/normal.woff2);unicode-range:U+0460-052F,U+1C80-1C88,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F;font-display:swap;}@font-face {font-family:Inter;font-style:normal;font-weight:700;src:url(/cf-fonts/v/inter/5.0.16/latin/wght/normal.woff2);unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+2074,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD;font-display:swap;}</style><link rel="stylesheet" href="./Get Quote_files/styles.e36af8e2.css">
<script src="./Get Quote_files/runtime~main.507e0f6f.js.下载" defer="defer"></script>
<script src="./Get Quote_files/main.0f0c42c1.js.下载" defer="defer"></script>
<meta name="viewport" content="width=device-width, initial-scale=1.0" data-rh="true"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5e95c892.99492ac7.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4b100ba1.cfb5c273.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a7bd4aaa.6894540a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/878e8ee7.9694acfa.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a94703ab.c1eb0a8a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5e95c892.99492ac7.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4b100ba1.cfb5c273.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a7bd4aaa.6894540a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/878e8ee7.9694acfa.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a94703ab.c1eb0a8a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5e95c892.99492ac7.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4b100ba1.cfb5c273.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a7bd4aaa.6894540a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/878e8ee7.9694acfa.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a94703ab.c1eb0a8a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/14eb3368.e080f423.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/6f563d2d.a76abe12.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5e95c892.99492ac7.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4b100ba1.cfb5c273.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a7bd4aaa.6894540a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/878e8ee7.9694acfa.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a94703ab.c1eb0a8a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5e95c892.99492ac7.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4b100ba1.cfb5c273.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a7bd4aaa.6894540a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/878e8ee7.9694acfa.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a94703ab.c1eb0a8a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5e95c892.99492ac7.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4b100ba1.cfb5c273.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a7bd4aaa.6894540a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/878e8ee7.9694acfa.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a94703ab.c1eb0a8a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/14eb3368.e080f423.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/d25969e9.17608258.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5e95c892.99492ac7.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4b100ba1.cfb5c273.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a7bd4aaa.6894540a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/878e8ee7.9694acfa.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a94703ab.c1eb0a8a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5e95c892.99492ac7.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4b100ba1.cfb5c273.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a7bd4aaa.6894540a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/878e8ee7.9694acfa.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a94703ab.c1eb0a8a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5e95c892.99492ac7.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4b100ba1.cfb5c273.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a7bd4aaa.6894540a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/878e8ee7.9694acfa.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a94703ab.c1eb0a8a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/14eb3368.e080f423.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/75758b27.b0804b5f.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5e95c892.99492ac7.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4b100ba1.cfb5c273.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a7bd4aaa.6894540a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/878e8ee7.9694acfa.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a94703ab.c1eb0a8a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5e95c892.99492ac7.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4b100ba1.cfb5c273.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a7bd4aaa.6894540a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/878e8ee7.9694acfa.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a94703ab.c1eb0a8a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5e95c892.99492ac7.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4b100ba1.cfb5c273.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a7bd4aaa.6894540a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/878e8ee7.9694acfa.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a94703ab.c1eb0a8a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/14eb3368.e080f423.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/22ca862a.5bf75dd3.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5e95c892.99492ac7.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4b100ba1.cfb5c273.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a7bd4aaa.6894540a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/878e8ee7.9694acfa.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a94703ab.c1eb0a8a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5e95c892.99492ac7.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4b100ba1.cfb5c273.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a7bd4aaa.6894540a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/878e8ee7.9694acfa.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a94703ab.c1eb0a8a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5e95c892.99492ac7.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4b100ba1.cfb5c273.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a7bd4aaa.6894540a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/878e8ee7.9694acfa.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a94703ab.c1eb0a8a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/14eb3368.e080f423.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a6853437.29fc9f41.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5e95c892.99492ac7.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4b100ba1.cfb5c273.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a7bd4aaa.6894540a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/878e8ee7.9694acfa.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a94703ab.c1eb0a8a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5e95c892.99492ac7.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4b100ba1.cfb5c273.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a7bd4aaa.6894540a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/878e8ee7.9694acfa.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a94703ab.c1eb0a8a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5e95c892.99492ac7.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4b100ba1.cfb5c273.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a7bd4aaa.6894540a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/878e8ee7.9694acfa.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a94703ab.c1eb0a8a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/14eb3368.e080f423.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/1a560ddb.02c09a8b.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5e95c892.99492ac7.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4b100ba1.cfb5c273.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a7bd4aaa.6894540a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/878e8ee7.9694acfa.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a94703ab.c1eb0a8a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5e95c892.99492ac7.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4b100ba1.cfb5c273.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a7bd4aaa.6894540a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/878e8ee7.9694acfa.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a94703ab.c1eb0a8a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5e95c892.99492ac7.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4b100ba1.cfb5c273.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a7bd4aaa.6894540a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/878e8ee7.9694acfa.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a94703ab.c1eb0a8a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/14eb3368.e080f423.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5c76b44c.85cea1f5.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/1df93b7f.8e883c60.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a7456010.f7c5b0ac.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5e95c892.99492ac7.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4b100ba1.cfb5c273.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a7bd4aaa.6894540a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/878e8ee7.9694acfa.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a94703ab.c1eb0a8a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5e95c892.99492ac7.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4b100ba1.cfb5c273.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a7bd4aaa.6894540a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/878e8ee7.9694acfa.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a94703ab.c1eb0a8a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5e95c892.99492ac7.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4b100ba1.cfb5c273.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a7bd4aaa.6894540a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/878e8ee7.9694acfa.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a94703ab.c1eb0a8a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4c5e977b.6b7e4d79.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/1d7495b4.fb00d542.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5e95c892.99492ac7.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4b100ba1.cfb5c273.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a7bd4aaa.6894540a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/878e8ee7.9694acfa.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a94703ab.c1eb0a8a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5e95c892.99492ac7.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4b100ba1.cfb5c273.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a7bd4aaa.6894540a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/878e8ee7.9694acfa.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a94703ab.c1eb0a8a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5e95c892.99492ac7.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4b100ba1.cfb5c273.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a7bd4aaa.6894540a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/878e8ee7.9694acfa.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a94703ab.c1eb0a8a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4c5e977b.6b7e4d79.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4a4acaf4.ef2eaa42.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5e95c892.99492ac7.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4b100ba1.cfb5c273.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a7bd4aaa.6894540a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/878e8ee7.9694acfa.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a94703ab.c1eb0a8a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5e95c892.99492ac7.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4b100ba1.cfb5c273.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a7bd4aaa.6894540a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/878e8ee7.9694acfa.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a94703ab.c1eb0a8a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5e95c892.99492ac7.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4b100ba1.cfb5c273.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a7bd4aaa.6894540a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/878e8ee7.9694acfa.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a94703ab.c1eb0a8a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4c5e977b.6b7e4d79.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/2fb4379a.c8d4320b.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5e95c892.99492ac7.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4b100ba1.cfb5c273.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a7bd4aaa.6894540a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/878e8ee7.9694acfa.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a94703ab.c1eb0a8a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5e95c892.99492ac7.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4b100ba1.cfb5c273.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a7bd4aaa.6894540a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/878e8ee7.9694acfa.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a94703ab.c1eb0a8a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5e95c892.99492ac7.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4b100ba1.cfb5c273.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a7bd4aaa.6894540a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/878e8ee7.9694acfa.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a94703ab.c1eb0a8a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4c5e977b.6b7e4d79.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/3bcfa024.08539ed7.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5e95c892.99492ac7.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4b100ba1.cfb5c273.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a7bd4aaa.6894540a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/878e8ee7.9694acfa.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a94703ab.c1eb0a8a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5e95c892.99492ac7.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4b100ba1.cfb5c273.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a7bd4aaa.6894540a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/878e8ee7.9694acfa.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a94703ab.c1eb0a8a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5e95c892.99492ac7.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4b100ba1.cfb5c273.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a7bd4aaa.6894540a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/878e8ee7.9694acfa.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a94703ab.c1eb0a8a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4c5e977b.6b7e4d79.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/8b53aafb.0b133100.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5e95c892.99492ac7.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4b100ba1.cfb5c273.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a7bd4aaa.6894540a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/878e8ee7.9694acfa.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a94703ab.c1eb0a8a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5e95c892.99492ac7.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4b100ba1.cfb5c273.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a7bd4aaa.6894540a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/878e8ee7.9694acfa.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a94703ab.c1eb0a8a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5e95c892.99492ac7.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4b100ba1.cfb5c273.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a7bd4aaa.6894540a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/878e8ee7.9694acfa.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a94703ab.c1eb0a8a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4c5e977b.6b7e4d79.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/16bc74e0.12e4becf.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5e95c892.99492ac7.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4b100ba1.cfb5c273.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a7bd4aaa.6894540a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/878e8ee7.9694acfa.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a94703ab.c1eb0a8a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5e95c892.99492ac7.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4b100ba1.cfb5c273.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a7bd4aaa.6894540a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/878e8ee7.9694acfa.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a94703ab.c1eb0a8a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5e95c892.99492ac7.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4b100ba1.cfb5c273.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a7bd4aaa.6894540a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/878e8ee7.9694acfa.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a94703ab.c1eb0a8a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4c5e977b.6b7e4d79.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a69e0386.2c68f9d8.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5e95c892.99492ac7.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4b100ba1.cfb5c273.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a7bd4aaa.6894540a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/878e8ee7.9694acfa.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a94703ab.c1eb0a8a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5e95c892.99492ac7.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4b100ba1.cfb5c273.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a7bd4aaa.6894540a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/878e8ee7.9694acfa.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a94703ab.c1eb0a8a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5e95c892.99492ac7.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4b100ba1.cfb5c273.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a7bd4aaa.6894540a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/878e8ee7.9694acfa.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a94703ab.c1eb0a8a.js"><link rel="canonical" href="https://dev.jup.ag/docs/swap-api/get-quote" data-rh="true"><link rel="alternate" href="https://dev.jup.ag/docs/swap-api/get-quote" hreflang="en" data-rh="true"><link rel="alternate" href="https://dev.jup.ag/docs/swap-api/get-quote" hreflang="x-default" data-rh="true"><meta property="og:url" content="https://dev.jup.ag/docs/swap-api/get-quote" data-rh="true"><meta property="og:title" content="Get Quote | Jupiter Developer Docs" data-rh="true"><meta name="description" content="Start using Jupiter Swap API by getting a Quote." data-rh="true"><meta property="og:description" content="Start using Jupiter Swap API by getting a Quote." data-rh="true"><meta name="twitter:card" content="summary" data-rh="true"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5e95c892.99492ac7.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4b100ba1.cfb5c273.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a7bd4aaa.6894540a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/878e8ee7.9694acfa.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a94703ab.c1eb0a8a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5e95c892.99492ac7.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4b100ba1.cfb5c273.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a7bd4aaa.6894540a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/878e8ee7.9694acfa.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a94703ab.c1eb0a8a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5e95c892.99492ac7.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4b100ba1.cfb5c273.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a7bd4aaa.6894540a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/878e8ee7.9694acfa.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a94703ab.c1eb0a8a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4c5e977b.6b7e4d79.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/f7a57768.3382ec94.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5e95c892.99492ac7.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4b100ba1.cfb5c273.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a7bd4aaa.6894540a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/878e8ee7.9694acfa.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a94703ab.c1eb0a8a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5e95c892.99492ac7.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4b100ba1.cfb5c273.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a7bd4aaa.6894540a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/878e8ee7.9694acfa.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a94703ab.c1eb0a8a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5e95c892.99492ac7.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4b100ba1.cfb5c273.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a7bd4aaa.6894540a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/878e8ee7.9694acfa.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a94703ab.c1eb0a8a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4c5e977b.6b7e4d79.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a98bd8bc.6509cb5d.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5e95c892.99492ac7.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4b100ba1.cfb5c273.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a7bd4aaa.6894540a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/878e8ee7.9694acfa.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a94703ab.c1eb0a8a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5e95c892.99492ac7.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4b100ba1.cfb5c273.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a7bd4aaa.6894540a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/878e8ee7.9694acfa.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a94703ab.c1eb0a8a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5e95c892.99492ac7.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4b100ba1.cfb5c273.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a7bd4aaa.6894540a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/878e8ee7.9694acfa.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a94703ab.c1eb0a8a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4c5e977b.6b7e4d79.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/bea9e49a.f1d8f6af.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5e95c892.99492ac7.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4b100ba1.cfb5c273.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a7bd4aaa.6894540a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/878e8ee7.9694acfa.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a94703ab.c1eb0a8a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5e95c892.99492ac7.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4b100ba1.cfb5c273.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a7bd4aaa.6894540a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/878e8ee7.9694acfa.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a94703ab.c1eb0a8a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5e95c892.99492ac7.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4b100ba1.cfb5c273.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a7bd4aaa.6894540a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/878e8ee7.9694acfa.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a94703ab.c1eb0a8a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4c5e977b.6b7e4d79.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/106303e0.346fcd07.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5e95c892.99492ac7.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4b100ba1.cfb5c273.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a7bd4aaa.6894540a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/878e8ee7.9694acfa.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a94703ab.c1eb0a8a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5e95c892.99492ac7.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4b100ba1.cfb5c273.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a7bd4aaa.6894540a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/878e8ee7.9694acfa.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a94703ab.c1eb0a8a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5e95c892.99492ac7.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4b100ba1.cfb5c273.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a7bd4aaa.6894540a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/878e8ee7.9694acfa.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a94703ab.c1eb0a8a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4c5e977b.6b7e4d79.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/e8826f25.0f7a522d.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5e95c892.99492ac7.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4b100ba1.cfb5c273.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a7bd4aaa.6894540a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/878e8ee7.9694acfa.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a94703ab.c1eb0a8a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5e95c892.99492ac7.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4b100ba1.cfb5c273.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a7bd4aaa.6894540a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/878e8ee7.9694acfa.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a94703ab.c1eb0a8a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5e95c892.99492ac7.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4b100ba1.cfb5c273.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a7bd4aaa.6894540a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/878e8ee7.9694acfa.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a94703ab.c1eb0a8a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4c5e977b.6b7e4d79.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5b149c85.a9a4dddf.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5e95c892.99492ac7.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4b100ba1.cfb5c273.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a7bd4aaa.6894540a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/878e8ee7.9694acfa.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a94703ab.c1eb0a8a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5e95c892.99492ac7.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4b100ba1.cfb5c273.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a7bd4aaa.6894540a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/878e8ee7.9694acfa.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a94703ab.c1eb0a8a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5e95c892.99492ac7.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4b100ba1.cfb5c273.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a7bd4aaa.6894540a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/878e8ee7.9694acfa.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a94703ab.c1eb0a8a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4c5e977b.6b7e4d79.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/7735337d.03d87f9e.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5e95c892.99492ac7.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4b100ba1.cfb5c273.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a7bd4aaa.6894540a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/878e8ee7.9694acfa.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a94703ab.c1eb0a8a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5e95c892.99492ac7.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4b100ba1.cfb5c273.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a7bd4aaa.6894540a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/878e8ee7.9694acfa.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a94703ab.c1eb0a8a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5e95c892.99492ac7.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4b100ba1.cfb5c273.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a7bd4aaa.6894540a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/878e8ee7.9694acfa.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a94703ab.c1eb0a8a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4c5e977b.6b7e4d79.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/554a5986.c117d9fe.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5e95c892.99492ac7.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4b100ba1.cfb5c273.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a7bd4aaa.6894540a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/878e8ee7.9694acfa.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a94703ab.c1eb0a8a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5e95c892.99492ac7.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4b100ba1.cfb5c273.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a7bd4aaa.6894540a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/878e8ee7.9694acfa.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a94703ab.c1eb0a8a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5e95c892.99492ac7.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4b100ba1.cfb5c273.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a7bd4aaa.6894540a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/878e8ee7.9694acfa.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a94703ab.c1eb0a8a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4c5e977b.6b7e4d79.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/0b7e8029.622c56a1.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5e95c892.99492ac7.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4b100ba1.cfb5c273.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a7bd4aaa.6894540a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/878e8ee7.9694acfa.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a94703ab.c1eb0a8a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5e95c892.99492ac7.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4b100ba1.cfb5c273.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a7bd4aaa.6894540a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/878e8ee7.9694acfa.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a94703ab.c1eb0a8a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5e95c892.99492ac7.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4b100ba1.cfb5c273.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a7bd4aaa.6894540a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/878e8ee7.9694acfa.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a94703ab.c1eb0a8a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4c5e977b.6b7e4d79.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4b37ff6f.783e2912.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5e95c892.99492ac7.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4b100ba1.cfb5c273.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a7bd4aaa.6894540a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/878e8ee7.9694acfa.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a94703ab.c1eb0a8a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5e95c892.99492ac7.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4b100ba1.cfb5c273.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a7bd4aaa.6894540a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/878e8ee7.9694acfa.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a94703ab.c1eb0a8a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5e95c892.99492ac7.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4b100ba1.cfb5c273.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a7bd4aaa.6894540a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/878e8ee7.9694acfa.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a94703ab.c1eb0a8a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4c5e977b.6b7e4d79.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/1983b526.6511ad64.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5e95c892.99492ac7.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4b100ba1.cfb5c273.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a7bd4aaa.6894540a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/878e8ee7.9694acfa.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a94703ab.c1eb0a8a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5e95c892.99492ac7.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4b100ba1.cfb5c273.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a7bd4aaa.6894540a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/878e8ee7.9694acfa.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a94703ab.c1eb0a8a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/5e95c892.99492ac7.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4b100ba1.cfb5c273.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a7bd4aaa.6894540a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/878e8ee7.9694acfa.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/a94703ab.c1eb0a8a.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/4c5e977b.6b7e4d79.js"><link rel="prefetch" href="https://dev.jup.ag/assets/js/9320422d.3fd27f75.js"></head>
<body class="" data-rh="class" style="overflow: visible;">
<script>!function(){function t(t){document.documentElement.setAttribute("data-theme",t)}var e=function(){try{return new URLSearchParams(window.location.search).get("docusaurus-theme")}catch(t){}}()||function(){try{return window.localStorage.getItem("theme")}catch(t){}}();t(null!==e?e:"light")}(),function(){try{const a=new URLSearchParams(window.location.search).entries();for(var[t,e]of a)if(t.startsWith("docusaurus-data-")){var n=t.replace("docusaurus-data-","data-");document.documentElement.setAttribute(n,e)}}catch(t){}}(),document.documentElement.setAttribute("data-announcement-bar-initially-dismissed",function(){try{return"true"===localStorage.getItem("docusaurus.announcement.dismiss")}catch(t){}return!1}())</script><div id="__docusaurus"><div role="region" aria-label="Skip to main content"><a class="skipToContent_fXgn" href="https://dev.jup.ag/docs/swap-api/get-quote#__docusaurus_skipToContent_fallback">Skip to main content</a></div><div class="announcementBar_mb4j" style="background-color:#FFA500;color:#000000" role="banner"><div class="content_knG7 announcementBarContent_xLdY">We have migrated our User Guides from Station to our new Support System. Please visit <a target="_blank" rel="noopener noreferrer" href="https://support.jup.ag/hc/en-us">Jupiter Helpdesk</a> for the latest guides.</div></div><nav aria-label="Main" class="navbar navbar--fixed-top"><div class="navbar__brand"><button aria-label="Toggle navigation bar" aria-expanded="false" class="navbar__toggle clean-btn" type="button"><svg width="30" height="30" viewBox="0 0 30 30" aria-hidden="true"><path stroke="currentColor" stroke-linecap="round" stroke-miterlimit="10" stroke-width="2" d="M4 7h22M4 15h22M4 23h22"></path></svg></button><a class="navbar__logo" href="https://dev.jup.ag/"><img src="./Get Quote_files/jupiter-logo.svg" alt="Jupiter Logo" class="themedImage..." height="28" width="28"></a><a href="https://dev.jup.ag/" class="navbar__title mobile-hidden"><span>Jupiter Develop</span></a></div><div class="navbar__items"><div class="navbar__item dropdown dropdown--hoverable"><div class="navbar__link-wrapper"><a href="https://dev.jup.ag/docs/" class="navbar__link navbar__link--active">APIs</a><span class="dropdown__trigger" aria-haspopup="true" aria-expanded="false" role="button"></span></div><ul class="dropdown__menu"><li><a class="dropdown__link" href="https://dev.jup.ag/docs/">Get Started</a></li><li><a class="dropdown__link" href="https://dev.jup.ag/docs/ultra-api/">Ultra API</a></li><li><a class="dropdown__link dropdown__link--active" href="https://dev.jup.ag/docs/swap-api/">Swap API</a></li><li><a class="dropdown__link" href="https://dev.jup.ag/docs/trigger-api/">Trigger API</a></li><li><a class="dropdown__link" href="https://dev.jup.ag/docs/recurring-api/">Recurring API</a></li><li><a class="dropdown__link" href="https://dev.jup.ag/docs/token-api/">Token API</a></li><li><a class="dropdown__link" href="https://dev.jup.ag/docs/price-api/">Price API</a></li><li><a class="dropdown__link" href="https://dev.jup.ag/docs/perp-api/">Perp API</a></li></ul></div><a class="navbar__item navbar__link" href="https://dev.jup.ag/docs/routing/">Routing Integration</a><a class="navbar__item navbar__link" href="https://dev.jup.ag/docs/tool-kits/">Tool Kits</a><a class="navbar__item navbar__link" href="https://dev.jup.ag/docs/api">API Reference</a><a class="navbar__item navbar__link" href="https://status.jup.ag/" target="_blank">Status</a><a class="navbar__item navbar__link" href="https://dev.jup.ag/docs/misc/">Misc</a></div><div class="navbar__items navbar__items--right"><div class="navbarSearchContainer"><button type="button" class="DocSearch DocSearch-Button" aria-label="Search (Ctrl+K)"><span class="DocSearch-Button-Container"><svg width="20" height="20" class="DocSearch-Search-Icon" viewBox="0 0 20 20" aria-hidden="true"><path d="M14.386 14.386l4.0877 4.0877-4.0877-4.0877c-2.9418 2.9419-7.7115 2.9419-10.6533 0-2.9419-2.9418-2.9419-7.7115 0-10.6533 2.9418-2.9419 7.7115-2.9419 10.6533 0 2.9419 2.9418 2.9419 7.7115 0 10.6533z" stroke="currentColor" fill="none" fill-rule="evenodd" stroke-linecap="round" stroke-linejoin="round"></path></svg><span class="DocSearch-Button-Placeholder">Search</span></span><span class="DocSearch-Button-Keys"><kbd class="DocSearch-Button-Key"><svg width="15" height="15" class="DocSearch-Control-Key-Icon"><path d="M4.505 4.496h2M5.505 5.496v5M8.216 4.496l.055 5.993M10 7.5c.333.333.5.667.5 1v2M12.326 4.5v5.996M8.384 4.496c1.674 0 2.116 0 2.116 1.5s-.442 1.5-2.116 1.5M3.205 9.303c-.09.448-.277 1.21-1.241 1.203C1 10.5.5 9.513.5 8V7c0-1.57.5-2.5 1.464-2.494.964.006 1.134.598 1.24 1.342M12.553 10.5h1.953" stroke-width="1.2" stroke="currentColor" fill="none" stroke-linecap="square"></path></svg></kbd><kbd class="DocSearch-Button-Key">K</kbd></span></button></div></div><div role="presentation" class="navbar-sidebar__backdrop"></div></nav><div id="__docusaurus_skipToContent_fallback" class="main-wrapper mainWrapper_z2l0"><div class="docsWrapper_hBAB"><button aria-label="Scroll back to top" class="clean-btn theme-back-to-top-button backToTopButton_sjWU" type="button"></button><div class="docRoot_UBD9"><aside class="theme-doc-sidebar-container docSidebarContainer_YfHR"><div class="sidebarViewport_aRkj"><div class="sidebar_njMd"><nav aria-label="Docs sidebar" class="menu thin-scrollbar menu_SIkG menuWithAnnouncementBar_GW3s"><ul class="theme-doc-sidebar-menu menu__list"><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-1 menu__list-item"><a class="menu__link" href="https://dev.jup.ag/docs/swap-api/">About Swap API</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-1"><div class="sidebar-line-break"></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item"><div class="menu__list-item-collapsible"><a class="menu__link menu__link--active">Swap API</a></div><ul class="menu__list" style="display: block; overflow: visible; height: auto;"><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link menu__link--active" aria-current="page" tabindex="0" href="https://dev.jup.ag/docs/swap-api/get-quote">Get Quote</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="https://dev.jup.ag/docs/swap-api/build-swap-transaction">Build Swap Transaction</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="https://dev.jup.ag/docs/swap-api/send-swap-transaction">Send Swap Transaction</a></li></ul></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-1"><div class="sidebar-line-break"></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item"><div class="menu__list-item-collapsible"><a class="menu__link">Swap API Guides</a></div><ul class="menu__list" style="display: block; overflow: visible; height: auto;"><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="https://dev.jup.ag/docs/swap-api/add-fees-to-swap">Add Fees To Swap</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="https://dev.jup.ag/docs/swap-api/payments-through-swap">Payments Through Swap</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="https://dev.jup.ag/docs/swap-api/requote-with-lower-max-accounts">Requote with Lower Max Accounts</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="https://dev.jup.ag/docs/swap-api/solana-unity-sdk">Swap In Solana Unity SDK</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="https://dev.jup.ag/docs/tool-kits/terminal/">Integrate Swap Terminal</a></li></ul></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-1"><div class="sidebar-line-break"></div></li><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item"><div class="menu__list-item-collapsible"><a class="menu__link">Debugging</a></div><ul class="menu__list" style="display: block; overflow: visible; height: auto;"><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="https://dev.jup.ag/docs/swap-api/common-errors">Common Errors</a></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link" tabindex="0" href="https://dev.jup.ag/docs/api-responses">API Responses</a></li></ul></li><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-1"><div class="sidebar-line-break"></div></li></ul></nav></div></div></aside><main class="docMainContainer_TBSr"><div class="container padding-top--md padding-bottom--lg"><div class="row"><div class="col docItemCol_xLCN"><div class="docItemContainer_jfFK"><article><nav class="theme-doc-breadcrumbs breadcrumbsContainer_Z_bl" aria-label="Breadcrumbs"><ul class="breadcrumbs" itemscope="" itemtype="https://schema.org/BreadcrumbList"><li class="breadcrumbs__item"><a aria-label="Home page" class="breadcrumbs__link" href="https://dev.jup.ag/"><svg viewBox="0 0 24 24" class="breadcrumbHomeIcon_YNFT"><path d="M10 19v-5h4v5c0 .55.45 1 1 1h3c.55 0 1-.45 1-1v-7h1.7c.46 0 .68-.57.33-.87L12.67 3.6c-.38-.34-.96-.34-1.34 0l-8.36 7.53c-.34.3-.13.87.33.87H5v7c0 .55.45 1 1 1h3c.55 0 1-.45 1-1z" fill="currentColor"></path></svg></a></li><li class="breadcrumbs__item"><span class="breadcrumbs__link">Swap API</span><meta itemprop="position" content="1"></li><li itemscope="" itemprop="itemListElement" itemtype="https://schema.org/ListItem" class="breadcrumbs__item breadcrumbs__item--active"><span class="breadcrumbs__link" itemprop="name">Get Quote</span><meta itemprop="position" content="2"></li></ul></nav><div class="tocCollapsible_ETCw theme-doc-toc-mobile tocMobile_ITEo"><button type="button" class="clean-btn tocCollapsibleButton_TO0P">On this page</button></div><div class="theme-doc-markdown markdown"><header><h1>Get Quote</h1></header><div class="row"><div class="col col--12 markdown">
<div class="theme-admonition theme-admonition-warning admonition_xJq3 alert alert--warning"><div class="admonitionHeading_Gvgb"><span class="admonitionIcon_Rf37"><svg viewBox="0 0 16 16"><path fill-rule="evenodd" d="M8.893 1.5c-.183-.31-.52-.5-.887-.5s-.703.19-.886.5L.138 13.499a.98.98 0 0 0 0 1.001c.193.31.53.501.886.501h13.964c.367 0 .704-.19.877-.5a1.03 1.03 0 0 0 .01-1.002L8.893 1.5zm.133 11.497H6.987v-2.003h2.039v2.003zm0-3.004H6.987V5.987h2.039v4.006z"></path></svg></span>Please use the Swap API at your own discretion.</div><div class="admonitionContent_BuS1"><p>The Jupiter UI at <a href="https://jup.ag/" target="_blank" rel="noopener noreferrer">https://jup.ag/</a> contains multiple safeguards, warnings and default settings to guide our users to trade safer. Jupiter is not liable for losses incurred by users on other platforms.</p><p>If you need clarification or support, please reach out to us in <a href="https://discord.gg/jup" target="_blank" rel="noopener noreferrer">Discord</a>.</p></div></div>
<div class="theme-admonition theme-admonition-warning admonition_xJq3 alert alert--warning"><div class="admonitionHeading_Gvgb"><span class="admonitionIcon_Rf37"><svg viewBox="0 0 16 16"><path fill-rule="evenodd" d="M8.893 1.5c-.183-.31-.52-.5-.887-.5s-.703.19-.886.5L.138 13.499a.98.98 0 0 0 0 1.001c.193.31.53.501.886.501h13.964c.367 0 .704-.19.877-.5a1.03 1.03 0 0 0 .01-1.002L8.893 1.5zm.133 11.497H6.987v-2.003h2.039v2.003zm0-3.004H6.987V5.987h2.039v4.006z"></path></svg></span>Routing Engine</div><div class="admonitionContent_BuS1"><p>The quotes from Swap API are from the Jupiter Metis v1 Routing Engine.</p></div></div>
<p>The Quote API enables you to tap into the Jupiter Metis v1 Routing Engine, which accesses the deep liquidity available within the DEXes of Solana's DeFi ecosystem. In this guide, we will walkthrough how you can get a quote for a specific token pair and other related parameters.</p>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="lets-get-started">Let’s Get Started<a href="https://dev.jup.ag/docs/swap-api/get-quote#lets-get-started" class="hash-link" aria-label="Direct link to Let’s Get Started" title="Direct link to Let’s Get Started">​</a></h2>
<p>In this guide, we will be using the Solana web3.js package.</p>
<p>If you have not set up your environment to use the necessary libraries and the connection to the Solana network, please head over to <a href="https://dev.jup.ag/docs/environment-setup">Environment Setup</a>.</p>
<div class="theme-admonition theme-admonition-tip admonition_xJq3 alert alert--success"><div class="admonitionHeading_Gvgb"><span class="admonitionIcon_Rf37"><svg viewBox="0 0 12 16"><path fill-rule="evenodd" d="M6.5 0C3.48 0 1 2.19 1 5c0 .92.55 2.25 1 3 1.34 2.25 1.78 2.78 2 4v1h5v-1c.22-1.22.66-1.75 2-4 .45-.75 1-2.08 1-3 0-2.81-2.48-5-5.5-5zm3.64 7.48c-.25.44-.47.8-.67 1.11-.86 1.41-1.25 2.06-1.45 3.23-.02.05-.02.11-.02.17H5c0-.06 0-.13-.02-.17-.2-1.17-.59-1.83-1.45-3.23-.2-.31-.42-.67-.67-1.11C2.44 6.78 2 5.65 2 5c0-2.2 2.02-4 4.5-4 1.22 0 2.36.42 3.22 1.19C10.55 2.94 11 3.94 11 5c0 .66-.44 1.78-.86 2.48zM4 14h5c-.23 1.14-1.3 2-2.5 2s-2.27-.86-2.5-2z"></path></svg></span>API Reference</div><div class="admonitionContent_BuS1"><p>To fully utilize the Quote API, check out the <a href="https://dev.jup.ag/docs/api/swap-api/quote">Quote API Reference</a>.</p></div></div>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="quote-api">Quote API<a href="https://dev.jup.ag/docs/swap-api/get-quote#quote-api" class="hash-link" aria-label="Direct link to Quote API" title="Direct link to Quote API">​</a></h2>
<div class="theme-admonition theme-admonition-note admonition_xJq3 alert alert--secondary"><div class="admonitionHeading_Gvgb"><span class="admonitionIcon_Rf37"><svg viewBox="0 0 14 16"><path fill-rule="evenodd" d="M6.3 5.69a.942.942 0 0 1-.28-.7c0-.28.09-.52.28-.7.19-.18.42-.28.7-.28.28 0 .**********.***********.28.7 0 .28-.09.52-.28.7a1 1 0 0 1-.7.3c-.28 0-.52-.11-.7-.3zM8 7.99c-.02-.25-.11-.48-.31-.69-.2-.19-.42-.3-.69-.31H6c-.27.02-.48.13-.69.31-.2.2-.3.44-.31.69h1v3c.**********.*********.42.31.69.31h1c.27 0 .48-.11.69-.31.2-.19.3-.42.31-.69H8V7.98v.01zM7 2.3c-3.14 0-5.7 2.54-5.7 5.68 0 3.14 2.56 5.7 5.7 5.7s5.7-2.55 5.7-5.7c0-3.15-2.56-5.69-5.7-5.69v.01zM7 .98c3.86 0 7 3.14 7 7s-3.14 7-7 7-7-3.12-7-7 3.14-7 7-7z"></path></svg></span>note</div><div class="admonitionContent_BuS1"><ul>
<li>Lite URL: <code>https://lite-api.jup.ag/quote</code></li>
<li>Pro URL: <code>https://api.jup.ag/swap/v1/quote</code></li>
</ul><p>To upgrade to Pro or understand our rate limiting, please refer to this section.</p><ul>
<li><a href="https://dev.jup.ag/docs/api-setup">API Key Setup</a></li>
<li><a href="https://dev.jup.ag/docs/api-rate-limit">API Rate Limit</a></li>
</ul></div></div>
<p>The most common trading pair on Solana is SOL and USDC, to get a quote for this specific token pair, you need to pass in the required parameters such as:</p>
<table><thead><tr><th>Parameters</th><th>Description</th></tr></thead><tbody><tr><td>inputMint</td><td>The pubkey or token mint address e.g. So11111111111111111111111111111111111111112</td></tr><tr><td>outputMint</td><td>The pubkey or token mint address e.g. EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v</td></tr><tr><td>amount</td><td>The number of <strong>input</strong> tokens before the decimal is applied, also known as the “raw amount” or “integer amount” in lamports for SOL or atomic units for all other tokens.</td></tr><tr><td>slippageBps</td><td>The number of basis points you can tolerate to lose during time of execution. e.g. 1% = 100bps</td></tr></tbody></table>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="get-quote">Get Quote<a href="https://dev.jup.ag/docs/swap-api/get-quote#get-quote" class="hash-link" aria-label="Direct link to Get Quote" title="Direct link to Get Quote">​</a></h2>
<p>Using the root URL and parameters to pass in, it is as simple as the example code below!</p>
<div class="language-jsx codeBlockContainer_Ckt0 theme-code-block" style="--prism-color: #393A34; --prism-background-color: #f6f8fa;"><div class="codeBlockContent_biex"><pre tabindex="0" class="prism-code language-jsx codeBlock_bY9V thin-scrollbar" style="color: rgb(57, 58, 52); background-color: rgb(246, 248, 250);"><code class="codeBlockLines_e6Vv"><span class="token-line" style="color: rgb(57, 58, 52);"><span class="token keyword" style="color: rgb(0, 0, 159);">const</span><span class="token plain"> quoteResponse </span><span class="token operator" style="color: rgb(57, 58, 52);">=</span><span class="token plain"> </span><span class="token keyword control-flow" style="color: rgb(0, 0, 159);">await</span><span class="token plain"> </span><span class="token punctuation" style="color: rgb(57, 58, 52);">(</span><span class="token plain"></span><br></span><span class="token-line" style="color: rgb(57, 58, 52);"><span class="token plain">    </span><span class="token keyword control-flow" style="color: rgb(0, 0, 159);">await</span><span class="token plain"> </span><span class="token function" style="color: rgb(215, 58, 73);">fetch</span><span class="token punctuation" style="color: rgb(57, 58, 52);">(</span><span class="token plain"></span><br></span><span class="token-line" style="color: rgb(57, 58, 52);"><span class="token plain">        </span><span class="token string" style="color: rgb(227, 17, 108);">'https://lite-api.jup.ag/swap/v1/quote?inputMint=So11111111111111111111111111111111111111112&amp;outputMint=EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v&amp;amount=100000000&amp;slippageBps=50&amp;restrictIntermediateTokens=true'</span><span class="token plain"></span><br></span><span class="token-line" style="color: rgb(57, 58, 52);"><span class="token plain">    </span><span class="token punctuation" style="color: rgb(57, 58, 52);">)</span><span class="token plain"></span><br></span><span class="token-line" style="color: rgb(57, 58, 52);"><span class="token plain">  </span><span class="token punctuation" style="color: rgb(57, 58, 52);">)</span><span class="token punctuation" style="color: rgb(57, 58, 52);">.</span><span class="token method function property-access" style="color: rgb(215, 58, 73);">json</span><span class="token punctuation" style="color: rgb(57, 58, 52);">(</span><span class="token punctuation" style="color: rgb(57, 58, 52);">)</span><span class="token punctuation" style="color: rgb(57, 58, 52);">;</span><span class="token plain"></span><br></span><span class="token-line" style="color: rgb(57, 58, 52);"><span class="token plain">  </span><br></span><span class="token-line" style="color: rgb(57, 58, 52);"><span class="token plain"></span><span class="token console class-name">console</span><span class="token punctuation" style="color: rgb(57, 58, 52);">.</span><span class="token method function property-access" style="color: rgb(215, 58, 73);">log</span><span class="token punctuation" style="color: rgb(57, 58, 52);">(</span><span class="token known-class-name class-name">JSON</span><span class="token punctuation" style="color: rgb(57, 58, 52);">.</span><span class="token method function property-access" style="color: rgb(215, 58, 73);">stringify</span><span class="token punctuation" style="color: rgb(57, 58, 52);">(</span><span class="token plain">quoteResponse</span><span class="token punctuation" style="color: rgb(57, 58, 52);">,</span><span class="token plain"> </span><span class="token keyword null nil" style="color: rgb(0, 0, 159);">null</span><span class="token punctuation" style="color: rgb(57, 58, 52);">,</span><span class="token plain"> </span><span class="token number" style="color: rgb(54, 172, 170);">2</span><span class="token punctuation" style="color: rgb(57, 58, 52);">)</span><span class="token punctuation" style="color: rgb(57, 58, 52);">)</span><span class="token punctuation" style="color: rgb(57, 58, 52);">;</span><br></span></code></pre><div class="buttonGroup__atx"><button type="button" class="clean-btn" aria-label="Toggle word wrap" title="Toggle word wrap"><svg viewBox="0 0 24 24" class="wordWrapButtonIcon_Bwma" aria-hidden="true"><path fill="currentColor" d="M4 19h6v-2H4v2zM20 5H4v2h16V5zm-3 6H4v2h13.25c1.1 0 2 .9 2 2s-.9 2-2 2H15v-2l-3 3l3 3v-2h2c2.21 0 4-1.79 4-4s-1.79-4-4-4z"></path></svg></button><button type="button" aria-label="Copy code to clipboard" title="Copy" class="clean-btn"><span class="copyButtonIcons_eSgA" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_y97N"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_LjdS"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<p>Example response:</p>
<div class="language-json codeBlockContainer_Ckt0 theme-code-block" style="--prism-color: #393A34; --prism-background-color: #f6f8fa;"><div class="codeBlockContent_biex"><pre tabindex="0" class="prism-code language-json codeBlock_bY9V thin-scrollbar" style="color: rgb(57, 58, 52); background-color: rgb(246, 248, 250);"><code class="codeBlockLines_e6Vv"><span class="token-line" style="color: rgb(57, 58, 52);"><span class="token punctuation" style="color: rgb(57, 58, 52);">{</span><span class="token plain"></span><br></span><span class="token-line" style="color: rgb(57, 58, 52);"><span class="token plain">  </span><span class="token property" style="color: rgb(54, 172, 170);">"inputMint"</span><span class="token operator" style="color: rgb(57, 58, 52);">:</span><span class="token plain"> </span><span class="token string" style="color: rgb(227, 17, 108);">"So11111111111111111111111111111111111111112"</span><span class="token punctuation" style="color: rgb(57, 58, 52);">,</span><span class="token plain"></span><br></span><span class="token-line" style="color: rgb(57, 58, 52);"><span class="token plain">  </span><span class="token property" style="color: rgb(54, 172, 170);">"inAmount"</span><span class="token operator" style="color: rgb(57, 58, 52);">:</span><span class="token plain"> </span><span class="token string" style="color: rgb(227, 17, 108);">"100000000"</span><span class="token punctuation" style="color: rgb(57, 58, 52);">,</span><span class="token plain"></span><br></span><span class="token-line" style="color: rgb(57, 58, 52);"><span class="token plain">  </span><span class="token property" style="color: rgb(54, 172, 170);">"outputMint"</span><span class="token operator" style="color: rgb(57, 58, 52);">:</span><span class="token plain"> </span><span class="token string" style="color: rgb(227, 17, 108);">"EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"</span><span class="token punctuation" style="color: rgb(57, 58, 52);">,</span><span class="token plain"></span><br></span><span class="token-line" style="color: rgb(57, 58, 52);"><span class="token plain">  </span><span class="token property" style="color: rgb(54, 172, 170);">"outAmount"</span><span class="token operator" style="color: rgb(57, 58, 52);">:</span><span class="token plain"> </span><span class="token string" style="color: rgb(227, 17, 108);">"16198753"</span><span class="token punctuation" style="color: rgb(57, 58, 52);">,</span><span class="token plain"></span><br></span><span class="token-line" style="color: rgb(57, 58, 52);"><span class="token plain">  </span><span class="token property" style="color: rgb(54, 172, 170);">"otherAmountThreshold"</span><span class="token operator" style="color: rgb(57, 58, 52);">:</span><span class="token plain"> </span><span class="token string" style="color: rgb(227, 17, 108);">"16117760"</span><span class="token punctuation" style="color: rgb(57, 58, 52);">,</span><span class="token plain"></span><br></span><span class="token-line" style="color: rgb(57, 58, 52);"><span class="token plain">  </span><span class="token property" style="color: rgb(54, 172, 170);">"swapMode"</span><span class="token operator" style="color: rgb(57, 58, 52);">:</span><span class="token plain"> </span><span class="token string" style="color: rgb(227, 17, 108);">"ExactIn"</span><span class="token punctuation" style="color: rgb(57, 58, 52);">,</span><span class="token plain"></span><br></span><span class="token-line" style="color: rgb(57, 58, 52);"><span class="token plain">  </span><span class="token property" style="color: rgb(54, 172, 170);">"slippageBps"</span><span class="token operator" style="color: rgb(57, 58, 52);">:</span><span class="token plain"> </span><span class="token number" style="color: rgb(54, 172, 170);">50</span><span class="token punctuation" style="color: rgb(57, 58, 52);">,</span><span class="token plain"></span><br></span><span class="token-line" style="color: rgb(57, 58, 52);"><span class="token plain">  </span><span class="token property" style="color: rgb(54, 172, 170);">"platformFee"</span><span class="token operator" style="color: rgb(57, 58, 52);">:</span><span class="token plain"> </span><span class="token null keyword" style="color: rgb(0, 0, 159);">null</span><span class="token punctuation" style="color: rgb(57, 58, 52);">,</span><span class="token plain"></span><br></span><span class="token-line" style="color: rgb(57, 58, 52);"><span class="token plain">  </span><span class="token property" style="color: rgb(54, 172, 170);">"priceImpactPct"</span><span class="token operator" style="color: rgb(57, 58, 52);">:</span><span class="token plain"> </span><span class="token string" style="color: rgb(227, 17, 108);">"0"</span><span class="token punctuation" style="color: rgb(57, 58, 52);">,</span><span class="token plain"></span><br></span><span class="token-line" style="color: rgb(57, 58, 52);"><span class="token plain">  </span><span class="token property" style="color: rgb(54, 172, 170);">"routePlan"</span><span class="token operator" style="color: rgb(57, 58, 52);">:</span><span class="token plain"> </span><span class="token punctuation" style="color: rgb(57, 58, 52);">[</span><span class="token plain"></span><br></span><span class="token-line" style="color: rgb(57, 58, 52);"><span class="token plain">    </span><span class="token punctuation" style="color: rgb(57, 58, 52);">{</span><span class="token plain"></span><br></span><span class="token-line" style="color: rgb(57, 58, 52);"><span class="token plain">      </span><span class="token property" style="color: rgb(54, 172, 170);">"swapInfo"</span><span class="token operator" style="color: rgb(57, 58, 52);">:</span><span class="token plain"> </span><span class="token punctuation" style="color: rgb(57, 58, 52);">{</span><span class="token plain"></span><br></span><span class="token-line" style="color: rgb(57, 58, 52);"><span class="token plain">        </span><span class="token property" style="color: rgb(54, 172, 170);">"ammKey"</span><span class="token operator" style="color: rgb(57, 58, 52);">:</span><span class="token plain"> </span><span class="token string" style="color: rgb(227, 17, 108);">"5BKxfWMbmYBAEWvyPZS9esPducUba9GqyMjtLCfbaqyF"</span><span class="token punctuation" style="color: rgb(57, 58, 52);">,</span><span class="token plain"></span><br></span><span class="token-line" style="color: rgb(57, 58, 52);"><span class="token plain">        </span><span class="token property" style="color: rgb(54, 172, 170);">"label"</span><span class="token operator" style="color: rgb(57, 58, 52);">:</span><span class="token plain"> </span><span class="token string" style="color: rgb(227, 17, 108);">"Meteora DLMM"</span><span class="token punctuation" style="color: rgb(57, 58, 52);">,</span><span class="token plain"></span><br></span><span class="token-line" style="color: rgb(57, 58, 52);"><span class="token plain">        </span><span class="token property" style="color: rgb(54, 172, 170);">"inputMint"</span><span class="token operator" style="color: rgb(57, 58, 52);">:</span><span class="token plain"> </span><span class="token string" style="color: rgb(227, 17, 108);">"So11111111111111111111111111111111111111112"</span><span class="token punctuation" style="color: rgb(57, 58, 52);">,</span><span class="token plain"></span><br></span><span class="token-line" style="color: rgb(57, 58, 52);"><span class="token plain">        </span><span class="token property" style="color: rgb(54, 172, 170);">"outputMint"</span><span class="token operator" style="color: rgb(57, 58, 52);">:</span><span class="token plain"> </span><span class="token string" style="color: rgb(227, 17, 108);">"EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"</span><span class="token punctuation" style="color: rgb(57, 58, 52);">,</span><span class="token plain"></span><br></span><span class="token-line" style="color: rgb(57, 58, 52);"><span class="token plain">        </span><span class="token property" style="color: rgb(54, 172, 170);">"inAmount"</span><span class="token operator" style="color: rgb(57, 58, 52);">:</span><span class="token plain"> </span><span class="token string" style="color: rgb(227, 17, 108);">"100000000"</span><span class="token punctuation" style="color: rgb(57, 58, 52);">,</span><span class="token plain"></span><br></span><span class="token-line" style="color: rgb(57, 58, 52);"><span class="token plain">        </span><span class="token property" style="color: rgb(54, 172, 170);">"outAmount"</span><span class="token operator" style="color: rgb(57, 58, 52);">:</span><span class="token plain"> </span><span class="token string" style="color: rgb(227, 17, 108);">"16198753"</span><span class="token punctuation" style="color: rgb(57, 58, 52);">,</span><span class="token plain"></span><br></span><span class="token-line" style="color: rgb(57, 58, 52);"><span class="token plain">        </span><span class="token property" style="color: rgb(54, 172, 170);">"feeAmount"</span><span class="token operator" style="color: rgb(57, 58, 52);">:</span><span class="token plain"> </span><span class="token string" style="color: rgb(227, 17, 108);">"24825"</span><span class="token punctuation" style="color: rgb(57, 58, 52);">,</span><span class="token plain"></span><br></span><span class="token-line" style="color: rgb(57, 58, 52);"><span class="token plain">        </span><span class="token property" style="color: rgb(54, 172, 170);">"feeMint"</span><span class="token operator" style="color: rgb(57, 58, 52);">:</span><span class="token plain"> </span><span class="token string" style="color: rgb(227, 17, 108);">"So11111111111111111111111111111111111111112"</span><span class="token plain"></span><br></span><span class="token-line" style="color: rgb(57, 58, 52);"><span class="token plain">      </span><span class="token punctuation" style="color: rgb(57, 58, 52);">}</span><span class="token punctuation" style="color: rgb(57, 58, 52);">,</span><span class="token plain"></span><br></span><span class="token-line" style="color: rgb(57, 58, 52);"><span class="token plain">      </span><span class="token property" style="color: rgb(54, 172, 170);">"percent"</span><span class="token operator" style="color: rgb(57, 58, 52);">:</span><span class="token plain"> </span><span class="token number" style="color: rgb(54, 172, 170);">100</span><span class="token plain"></span><br></span><span class="token-line" style="color: rgb(57, 58, 52);"><span class="token plain">    </span><span class="token punctuation" style="color: rgb(57, 58, 52);">}</span><span class="token plain"></span><br></span><span class="token-line" style="color: rgb(57, 58, 52);"><span class="token plain">  </span><span class="token punctuation" style="color: rgb(57, 58, 52);">]</span><span class="token punctuation" style="color: rgb(57, 58, 52);">,</span><span class="token plain"></span><br></span><span class="token-line" style="color: rgb(57, 58, 52);"><span class="token plain">  </span><span class="token property" style="color: rgb(54, 172, 170);">"contextSlot"</span><span class="token operator" style="color: rgb(57, 58, 52);">:</span><span class="token plain"> </span><span class="token number" style="color: rgb(54, 172, 170);">299283763</span><span class="token punctuation" style="color: rgb(57, 58, 52);">,</span><span class="token plain"></span><br></span><span class="token-line" style="color: rgb(57, 58, 52);"><span class="token plain">  </span><span class="token property" style="color: rgb(54, 172, 170);">"timeTaken"</span><span class="token operator" style="color: rgb(57, 58, 52);">:</span><span class="token plain"> </span><span class="token number" style="color: rgb(54, 172, 170);">0.015257836</span><span class="token plain"></span><br></span><span class="token-line" style="color: rgb(57, 58, 52);"><span class="token plain"></span><span class="token punctuation" style="color: rgb(57, 58, 52);">}</span><br></span></code></pre><div class="buttonGroup__atx"><button type="button" aria-label="Copy code to clipboard" title="Copy" class="clean-btn"><span class="copyButtonIcons_eSgA" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_y97N"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_LjdS"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<div class="theme-admonition theme-admonition-tip admonition_xJq3 alert alert--success"><div class="admonitionHeading_Gvgb"><span class="admonitionIcon_Rf37"><svg viewBox="0 0 12 16"><path fill-rule="evenodd" d="M6.5 0C3.48 0 1 2.19 1 5c0 .92.55 2.25 1 3 1.34 2.25 1.78 2.78 2 4v1h5v-1c.22-1.22.66-1.75 2-4 .45-.75 1-2.08 1-3 0-2.81-2.48-5-5.5-5zm3.64 7.48c-.25.44-.47.8-.67 1.11-.86 1.41-1.25 2.06-1.45 3.23-.02.05-.02.11-.02.17H5c0-.06 0-.13-.02-.17-.2-1.17-.59-1.83-1.45-3.23-.2-.31-.42-.67-.67-1.11C2.44 6.78 2 5.65 2 5c0-2.2 2.02-4 4.5-4 1.22 0 2.36.42 3.22 1.19C10.55 2.94 11 3.94 11 5c0 .66-.44 1.78-.86 2.48zM4 14h5c-.23 1.14-1.3 2-2.5 2s-2.27-.86-2.5-2z"></path></svg></span>tip</div><div class="admonitionContent_BuS1"><p><code>outAmount</code> refers to the best possible output amount based on the route at time of quote, this means that <code>slippageBps</code> does not affect.</p></div></div>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="whats-next">What’s Next<a href="https://dev.jup.ag/docs/swap-api/get-quote#whats-next" class="hash-link" aria-label="Direct link to What’s Next" title="Direct link to What’s Next">​</a></h2>
<p>Now, you are able to get a quote, next steps is to submit a transaction to execute the swap based on the quote given. Let’s go!</p>
<hr>
<h2 class="anchor anchorWithStickyNavbar_LWe7" id="additional-resources">Additional Resources<a href="https://dev.jup.ag/docs/swap-api/get-quote#additional-resources" class="hash-link" aria-label="Direct link to Additional Resources" title="Direct link to Additional Resources">​</a></h2>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="restrict-intermediate-tokens">Restrict Intermediate Tokens<a href="https://dev.jup.ag/docs/swap-api/get-quote#restrict-intermediate-tokens" class="hash-link" aria-label="Direct link to Restrict Intermediate Tokens" title="Direct link to Restrict Intermediate Tokens">​</a></h3>
<p><code>restrictIntermediateTokens</code> can be set to&nbsp;<code>true</code> . If your route is routed through random intermediate tokens, it will fail more frequently. With this, we make sure that your route is only routed through highly liquid intermediate tokens to give you the best price and more stable route.</p>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="legacy-transactions">Legacy Transactions<a href="https://dev.jup.ag/docs/swap-api/get-quote#legacy-transactions" class="hash-link" aria-label="Direct link to Legacy Transactions" title="Direct link to Legacy Transactions">​</a></h3>
<p>All Jupiter swaps are using Versioned Transactions and <a href="https://docs.solana.com/developing/lookup-tables" target="_blank" rel="noopener noreferrer">Address Lookup Tables</a>. However, not all wallets support Versioned Transactions yet, so if you detect a wallet that does not support versioned transactions, you will need to set the&nbsp;<code>asLegacyTransaction</code>&nbsp;parameter to <code>true</code>.</p>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="adding-fees">Adding Fees<a href="https://dev.jup.ag/docs/swap-api/get-quote#adding-fees" class="hash-link" aria-label="Direct link to Adding Fees" title="Direct link to Adding Fees">​</a></h3>
<p>By using the Quote API in your app, you can add a fee to charge your users. You can refer to the <code>platformFeeBps</code> parameter and to add it to your quote and in conjuction, add <code>feeAccount</code> (it can be any valid token account) to your swap request.</p>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="direct-routes">Direct Routes<a href="https://dev.jup.ag/docs/swap-api/get-quote#direct-routes" class="hash-link" aria-label="Direct link to Direct Routes" title="Direct link to Direct Routes">​</a></h3>
<p>In some cases, you may want to restrict the routing to only go through 1 market. You can use the <code>onlyDirectRoutes</code> parameter to achieve this. This will ensure routing will only go through 1 market.</p>
<div class="theme-admonition theme-admonition-note admonition_xJq3 alert alert--secondary"><div class="admonitionHeading_Gvgb"><span class="admonitionIcon_Rf37"><svg viewBox="0 0 14 16"><path fill-rule="evenodd" d="M6.3 5.69a.942.942 0 0 1-.28-.7c0-.28.09-.52.28-.7.19-.18.42-.28.7-.28.28 0 .**********.***********.28.7 0 .28-.09.52-.28.7a1 1 0 0 1-.7.3c-.28 0-.52-.11-.7-.3zM8 7.99c-.02-.25-.11-.48-.31-.69-.2-.19-.42-.3-.69-.31H6c-.27.02-.48.13-.69.31-.2.2-.3.44-.31.69h1v3c.**********.*********.42.31.69.31h1c.27 0 .48-.11.69-.31.2-.19.3-.42.31-.69H8V7.98v.01zM7 2.3c-3.14 0-5.7 2.54-5.7 5.68 0 3.14 2.56 5.7 5.7 5.7s5.7-2.55 5.7-5.7c0-3.15-2.56-5.69-5.7-5.69v.01zM7 .98c3.86 0 7 3.14 7 7s-3.14 7-7 7-7-3.12-7-7 3.14-7 7-7z"></path></svg></span>note</div><div class="admonitionContent_BuS1"><ul>
<li>If there are no direct routes, there will be no quote.</li>
<li>If there is only 1 market but it is illiquid, it will still return the route with the illiquid market.</li>
</ul></div></div>
<div class="theme-admonition theme-admonition-warning admonition_xJq3 alert alert--warning"><div class="admonitionHeading_Gvgb"><span class="admonitionIcon_Rf37"><svg viewBox="0 0 16 16"><path fill-rule="evenodd" d="M8.893 1.5c-.183-.31-.52-.5-.887-.5s-.703.19-.886.5L.138 13.499a.98.98 0 0 0 0 1.001c.193.31.53.501.886.501h13.964c.367 0 .704-.19.877-.5a1.03 1.03 0 0 0 .01-1.002L8.893 1.5zm.133 11.497H6.987v-2.003h2.039v2.003zm0-3.004H6.987V5.987h2.039v4.006z"></path></svg></span>unfavorable trades</div><div class="admonitionContent_BuS1"><p>Please be aware that using <code>onlyDirectRoutes</code> can often yield unfavorable trades or outcomes.</p></div></div>
<h3 class="anchor anchorWithStickyNavbar_LWe7" id="max-accounts">Max Accounts<a href="https://dev.jup.ag/docs/swap-api/get-quote#max-accounts" class="hash-link" aria-label="Direct link to Max Accounts" title="Direct link to Max Accounts">​</a></h3>
<p>In some cases, you may want to add more accounts to the transaction for specific use cases, but it might exceed the transaction size limit. You can use the <code>maxAccounts</code> parameter to limit the number of accounts in the transaction.</p>
<div class="theme-admonition theme-admonition-warning admonition_xJq3 alert alert--warning"><div class="admonitionHeading_Gvgb"><span class="admonitionIcon_Rf37"><svg viewBox="0 0 16 16"><path fill-rule="evenodd" d="M8.893 1.5c-.183-.31-.52-.5-.887-.5s-.703.19-.886.5L.138 13.499a.98.98 0 0 0 0 1.001c.193.31.53.501.886.501h13.964c.367 0 .704-.19.877-.5a1.03 1.03 0 0 0 .01-1.002L8.893 1.5zm.133 11.497H6.987v-2.003h2.039v2.003zm0-3.004H6.987V5.987h2.039v4.006z"></path></svg></span>unfavorable trades</div><div class="admonitionContent_BuS1"><p>Please be aware that the misuse of <code>maxAccounts</code> can yield unfavorable trades or outcomes.</p></div></div>
<div class="theme-admonition theme-admonition-tip admonition_xJq3 alert alert--success"><div class="admonitionHeading_Gvgb"><span class="admonitionIcon_Rf37"><svg viewBox="0 0 12 16"><path fill-rule="evenodd" d="M6.5 0C3.48 0 1 2.19 1 5c0 .92.55 2.25 1 3 1.34 2.25 1.78 2.78 2 4v1h5v-1c.22-1.22.66-1.75 2-4 .45-.75 1-2.08 1-3 0-2.81-2.48-5-5.5-5zm3.64 7.48c-.25.44-.47.8-.67 1.11-.86 1.41-1.25 2.06-1.45 3.23-.02.05-.02.11-.02.17H5c0-.06 0-.13-.02-.17-.2-1.17-.59-1.83-1.45-3.23-.2-.31-.42-.67-.67-1.11C2.44 6.78 2 5.65 2 5c0-2.2 2.02-4 4.5-4 1.22 0 2.36.42 3.22 1.19C10.55 2.94 11 3.94 11 5c0 .66-.44 1.78-.86 2.48zM4 14h5c-.23 1.14-1.3 2-2.5 2s-2.27-.86-2.5-2z"></path></svg></span>tip</div><div class="admonitionContent_BuS1"><ul>
<li>Refer to the <a href="https://dev.jup.ag/docs/swap-api/requote-with-lower-max-accounts">Requote with Lower Max Accounts</a> guide for more information on how to requote and adjust the swap when using <code>maxAccounts</code>.</li>
</ul></div></div>
<div class="theme-admonition theme-admonition-note admonition_xJq3 alert alert--secondary"><div class="admonitionHeading_Gvgb"><span class="admonitionIcon_Rf37"><svg viewBox="0 0 14 16"><path fill-rule="evenodd" d="M6.3 5.69a.942.942 0 0 1-.28-.7c0-.28.09-.52.28-.7.19-.18.42-.28.7-.28.28 0 .**********.***********.28.7 0 .28-.09.52-.28.7a1 1 0 0 1-.7.3c-.28 0-.52-.11-.7-.3zM8 7.99c-.02-.25-.11-.48-.31-.69-.2-.19-.42-.3-.69-.31H6c-.27.02-.48.13-.69.31-.2.2-.3.44-.31.69h1v3c.**********.*********.42.31.69.31h1c.27 0 .48-.11.69-.31.2-.19.3-.42.31-.69H8V7.98v.01zM7 2.3c-3.14 0-5.7 2.54-5.7 5.68 0 3.14 2.56 5.7 5.7 5.7s5.7-2.55 5.7-5.7c0-3.15-2.56-5.69-5.7-5.69v.01zM7 .98c3.86 0 7 3.14 7 7s-3.14 7-7 7-7-3.12-7-7 3.14-7 7-7z"></path></svg></span>note</div><div class="admonitionContent_BuS1"><ul>
<li><code>maxAccounts</code> is an estimation and the actual number of accounts may vary.</li>
<li><code>maxAccounts</code> only applies to the total number of accounts of the inner swaps in the swap instruction and not any of the setup, cleanup or other instructions (see the example below).</li>
<li>We recommend setting <code>maxAccounts</code> to 64</li>
<li>Keep <code>maxAccounts</code> as large as possible, only reduce <code>maxAccounts</code> if you exceed the transaction size limit.</li>
<li>If <code>maxAccounts</code> is set too low, example to 30, the computed route may drop DEXes/AMMs like Meteora DLMM that require more than 30 accounts.</li>
</ul><br><p><strong>Jupiter has 2 types of routing instructions</strong>, if you plan to limit <code>maxAccounts</code>, you will need to account for if the market is routable with <a href="https://docs.solana.com/developing/lookup-tables" target="_blank" rel="noopener noreferrer">ALTs</a> or not:</p><ul>
<li><strong><code>Routing Instruction</code></strong> (Simple Routing): The market is still new, and we do not have ALTs set up for the market, hence the number of accounts required is higher as there are more accounts required.</li>
<li><strong><code>Shared Accounts Routing Instruction</code></strong>: The market has sufficient liquidity (and has been live for a while), and we have <a href="https://docs.solana.com/developing/lookup-tables" target="_blank" rel="noopener noreferrer">ALTs</a> set up for the market to be used in the routing instruction, hence the number of accounts required is lower as there are less accounts required.</li>
</ul></div></div>
<details class="details_lb9f isBrowser_bmU9 alert alert--info details_b_Ee" data-collapsed="true"><summary><div><div><b>Counting the accounts using an example transaction</b></div></div></summary><div style="display: none; overflow: hidden; height: 0px;"><div class="collapsibleContent_i85q"><p><a href="https://solscan.io/tx/2xpiniSn5z61hE6gB6EUaeRZCqeg8rLBEbiSnAjSD28tjVTSpBogSLfrMRaJiDzuqDyZ8v49Z7WL2TKvGQVwYbB7" target="_blank" rel="noopener noreferrer">In this transaction</a>:</p><img src="./Get Quote_files/max_accounts_stabble.png" alt="Max Accounts Stabble Example" style="width: 50%;"><img src="./Get Quote_files/max_accounts_lifinity_v2.png" alt="Max Accounts Lifinity V2 Example" style="width: 50%;"><img src="./Get Quote_files/max_accounts_shared_accounts_route.png" alt="Max Accounts Shared Accounts Route Example" style="width: 50%;"><ul>
<li>You can see that there are a total of 2 inner swaps where the number of accounts respectively are
<ul>
<li>Stabble Stable Swap: 12</li>
<li>Lifinity Swap V2: 13</li>
<li>Total: 25</li>
</ul>
</li>
<li>The <code>maxAccounts</code> parameter is to control this value - to limit the total number of accounts in the inner swaps.</li>
<li>It doesn’t take into the consideration of a few things:
<ul>
<li>Each of the inner swap's program address, so 2 in this case.</li>
<li>Top level routing instruction accounts where in this case Shared Accounts Route is 13 and Route is 9.</li>
<li>There are also other accounts that are required to set up, clean up, etc which are not counted in the <code>maxAccounts</code> parameter</li>
</ul>
</li>
</ul></div></div></details>
<details class="details_lb9f isBrowser_bmU9 alert alert--info details_b_Ee" data-collapsed="true"><summary><div><div><b>List of DEXes and their required accounts</b></div></div></summary><div style="display: none; overflow: hidden; height: 0px;"><div class="collapsibleContent_i85q"><p>Notes:</p><ul>
<li>Values in the table are only estimations and the actual number of accounts may vary.</li>
<li>Min accounts are needed when we have already created the necessary <a href="https://docs.solana.com/developing/lookup-tables" target="_blank" rel="noopener noreferrer">ALTs</a> for a specific pool resulting in less accounts needed in a Shared Accounts Routing context.</li>
<li>Sanctum and Sanctum Infinity are unique, and their accounts are dynamic.</li>
</ul><table><thead><tr><th>DEX</th><th>Max</th><th>Min</th></tr></thead><tbody><tr><td>Meteora DLMM</td><td>47</td><td>19</td></tr><tr><td>Meteora</td><td>45</td><td>18</td></tr><tr><td>Moonshot</td><td>37</td><td>15</td></tr><tr><td>Obric</td><td>30</td><td>12</td></tr><tr><td>Orca Whirlpool</td><td>30</td><td>12</td></tr><tr><td>Pumpfun AMM</td><td>42</td><td>17</td></tr><tr><td>Pumpfun Bonding Curve</td><td>40</td><td>16</td></tr><tr><td>Raydium</td><td>45</td><td>18</td></tr><tr><td>Raydium CLMM</td><td>45</td><td>19</td></tr><tr><td>Raydium CPMM</td><td>37</td><td>14</td></tr><tr><td>Sanctum</td><td>80</td><td>80</td></tr><tr><td>Sanctum Infinity</td><td>80</td><td>80</td></tr><tr><td>Solfi</td><td>22</td><td>9</td></tr></tbody></table></div></div></details></div></div></div><div class="row"><div class="col col--12"><footer class="theme-doc-footer docusaurus-mt-lg"><div class="row margin-top--sm theme-doc-footer-edit-meta-row"><div class="col"><a href="https://github.com/jup-ag/docs/tree/main/docs/100-swap-api/1-get-quote.md" target="_blank" rel="noopener noreferrer" class="theme-edit-this-page"><svg fill="currentColor" height="20" width="20" viewBox="0 0 40 40" class="iconEdit_Z9Sw" aria-hidden="true"><g><path d="m34.5 11.7l-3 3.1-6.3-6.3 3.1-3q0.5-0.5 1.2-0.5t1.1 0.5l3.9 3.9q0.5 0.4 0.5 1.1t-0.5 1.2z m-29.5 17.1l18.4-18.5 6.3 6.3-18.4 18.4h-6.3v-6.2z"></path></g></svg>Edit this page</a></div><div class="col lastUpdated_JAkA"></div></div></footer></div></div></article><div class="row"><div class="col col--12"><nav class="pagination-nav docusaurus-mt-lg" aria-label="Docs pages"><a class="pagination-nav__link pagination-nav__link--prev" href="https://dev.jup.ag/docs/swap-api/"><div class="pagination-nav__sublabel">Previous</div><div class="pagination-nav__label">About Swap API</div></a><a class="pagination-nav__link pagination-nav__link--next" href="https://dev.jup.ag/docs/swap-api/build-swap-transaction"><div class="pagination-nav__sublabel">Next</div><div class="pagination-nav__label">Build Swap Transaction</div></a></nav></div></div></div></div><div class="col col--3"><div class="tableOfContents_bqdL thin-scrollbar theme-doc-toc-desktop"><ul class="table-of-contents table-of-contents__left-border"><li><a href="https://dev.jup.ag/docs/swap-api/get-quote#lets-get-started" class="table-of-contents__link toc-highlight">Let’s Get Started</a></li><li><a href="https://dev.jup.ag/docs/swap-api/get-quote#quote-api" class="table-of-contents__link toc-highlight">Quote API</a></li><li><a href="https://dev.jup.ag/docs/swap-api/get-quote#get-quote" class="table-of-contents__link toc-highlight">Get Quote</a></li><li><a href="https://dev.jup.ag/docs/swap-api/get-quote#whats-next" class="table-of-contents__link toc-highlight">What’s Next</a></li><li><a href="https://dev.jup.ag/docs/swap-api/get-quote#additional-resources" class="table-of-contents__link toc-highlight">Additional Resources</a><ul><li><a href="https://dev.jup.ag/docs/swap-api/get-quote#restrict-intermediate-tokens" class="table-of-contents__link toc-highlight">Restrict Intermediate Tokens</a></li><li><a href="https://dev.jup.ag/docs/swap-api/get-quote#legacy-transactions" class="table-of-contents__link toc-highlight">Legacy Transactions</a></li><li><a href="https://dev.jup.ag/docs/swap-api/get-quote#adding-fees" class="table-of-contents__link toc-highlight">Adding Fees</a></li><li><a href="https://dev.jup.ag/docs/swap-api/get-quote#direct-routes" class="table-of-contents__link toc-highlight">Direct Routes</a></li><li><a href="https://dev.jup.ag/docs/swap-api/get-quote#max-accounts" class="table-of-contents__link toc-highlight">Max Accounts</a></li></ul></li></ul></div></div></div></div></main></div></div></div></div>
<script defer="" src="./Get Quote_files/vcd15cbe7772f49c399c6a5babf22c1241717689176015" integrity="sha512-ZpsOmlRQV6y907TI0dKBHq9Md29nnaEIPlkf84rnaERnq6zvWvPUqr2ft8M1aS28oN72PdrCzSjY4U6VaAw1EQ==" data-cf-beacon="{&quot;rayId&quot;:&quot;95f4cd88681ebf9f&quot;,&quot;serverTiming&quot;:{&quot;name&quot;:{&quot;cfExtPri&quot;:true,&quot;cfEdge&quot;:true,&quot;cfOrigin&quot;:true,&quot;cfL4&quot;:true,&quot;cfSpeedBrain&quot;:true,&quot;cfCacheStatus&quot;:true}},&quot;version&quot;:&quot;2025.6.2&quot;,&quot;token&quot;:&quot;ac4d3bdcea374badae66954bff9555fc&quot;}" crossorigin="anonymous"></script>

</body></html>