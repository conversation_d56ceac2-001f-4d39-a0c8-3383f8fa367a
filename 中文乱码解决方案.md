# PUMP30 中文乱码解决方案

## 问题描述
当使用 `java -jar pump30.jar` 直接启动程序时，中文字符显示为乱码：
```
鎵ц浠锋牸鐩戞帶浠诲姟 #1  // 应该显示：执行价格监控任务 #1
```

## 解决方案

### 推荐方法1：使用PowerShell脚本
```powershell
.\start-pump-utf8.ps1
```

### 推荐方法2：使用批处理脚本  
```cmd
start-pump-utf8.bat
```

### 方法3：手动命令行启动
```powershell
java -Dfile.encoding=UTF-8 -Dconsole.encoding=UTF-8 -Dsun.jnu.encoding=UTF-8 -Duser.timezone=Asia/Shanghai -jar pump30.jar
```

## 原理说明

### 问题根源
1. Windows系统默认使用GBK编码（代码页936）
2. Java程序输出的UTF-8编码的中文被GBK解释，造成乱码
3. JVM的 `file.encoding` 属性必须在启动时设置

### 解决原理
1. **设置控制台代码页**：`chcp 65001` 将控制台设置为UTF-8
2. **设置JVM编码参数**：
   - `-Dfile.encoding=UTF-8`：设置文件编码
   - `-Dconsole.encoding=UTF-8`：设置控制台编码  
   - `-Dsun.jnu.encoding=UTF-8`：设置JNU编码
3. **设置时区**：`-Duser.timezone=Asia/Shanghai`

## 验证方法

启动后应该看到正确的中文显示：
```
执行价格监控任务 #1
PUMP价格监控系统已启动，监控间隔: 200ms
报警配置 - 启用: true, 买入阈值: $30.00, 卖出阈值: $30.00
```

## 注意事项

1. **重要**：不要使用 `java -jar pump30.jar` 直接启动
2. PowerShell脚本可能需要执行策略权限，如果提示错误，使用批处理脚本
3. 确保终端字体支持中文显示（如微软雅黑、SimSun等）

## 常见问题

### Q: PowerShell脚本无法执行？
A: 运行 `Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser` 后重试

### Q: 仍然乱码？
A: 检查终端字体设置，确保支持UTF-8和中文字符

### Q: 想要永久解决？
A: 始终使用提供的启动脚本，不要直接使用java命令 