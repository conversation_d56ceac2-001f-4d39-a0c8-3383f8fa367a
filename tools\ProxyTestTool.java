import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.net.URL;

/**
 * 代理连接测试工具
 * 测试不同代理配置对Jupiter API的访问效果
 * 
 * <AUTHOR> Agent
 * @version 1.0
 * @since 2025-01-15
 */
public class ProxyTestTool {
    
    private static final String PUMP_TOKEN = "83cXjb9Z8iJSBK2DBacte1utUsygdotMBQVhJoSppump";
    private static final String API_URL = "https://lite-api.jup.ag/price/v3?ids=" + PUMP_TOKEN;
    private static final String HEALTH_URL = "https://lite-api.jup.ag/health";
    
    public static void main(String[] args) {
        System.out.println("=== Jupiter API 代理连接测试工具 ===");
        System.out.println();
        
        // 测试1: 直接连接
        System.out.println("1. 测试直接连接...");
        testConnection("直接连接", null);
        
        // 测试2: HTTP代理
        System.out.println("\n2. 测试HTTP代理...");
        Proxy httpProxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress("127.0.0.1", 7890));
        testConnection("HTTP代理", httpProxy);
        
        // 测试3: SOCKS代理
        System.out.println("\n3. 测试SOCKS代理...");
        Proxy socksProxy = new Proxy(Proxy.Type.SOCKS, new InetSocketAddress("127.0.0.1", 7890));
        testConnection("SOCKS代理", socksProxy);
        
        // 测试4: 健康检查端点
        System.out.println("\n4. 测试健康检查端点...");
        testHealthEndpoint();
        
        System.out.println("\n=== 测试完成 ===");
        System.out.println("\n建议:");
        System.out.println("1. 如果直接连接失败，请启用代理");
        System.out.println("2. 如果HTTP代理返回403，尝试SOCKS代理");
        System.out.println("3. 检查代理软件是否允许访问 lite-api.jup.ag");
        System.out.println("4. 考虑使用系统代理设置");
    }
    
    /**
     * 测试连接
     */
    private static void testConnection(String testName, Proxy proxy) {
        try {
            URL url = new URL(API_URL);
            HttpURLConnection connection;
            
            if (proxy != null) {
                connection = (HttpURLConnection) url.openConnection(proxy);
            } else {
                connection = (HttpURLConnection) url.openConnection();
            }
            
            // 设置请求属性
            connection.setRequestMethod("GET");
            connection.setRequestProperty("User-Agent", "PUMP-Monitor/1.0");
            connection.setRequestProperty("Accept", "application/json");
            connection.setConnectTimeout(10000);
            connection.setReadTimeout(15000);
            
            // 发送请求
            int responseCode = connection.getResponseCode();
            System.out.println("  " + testName + " - 响应代码: " + responseCode);
            
            if (responseCode == 200) {
                // 读取响应
                BufferedReader reader = new BufferedReader(
                    new InputStreamReader(connection.getInputStream()));
                StringBuilder response = new StringBuilder();
                String line;
                
                while ((line = reader.readLine()) != null) {
                    response.append(line);
                }
                reader.close();
                
                String responseBody = response.toString();
                if (responseBody.contains(PUMP_TOKEN) && responseBody.contains("usdPrice")) {
                    System.out.println("  ✅ " + testName + " 成功 - 获取到价格数据");
                } else {
                    System.out.println("  ⚠️ " + testName + " 部分成功 - 响应格式异常");
                }
                
            } else if (responseCode == 403) {
                System.out.println("  ❌ " + testName + " 失败 - 403 访问被拒绝");
                System.out.println("    建议: 检查代理软件访问控制设置");
                
            } else {
                System.out.println("  ❌ " + testName + " 失败 - HTTP " + responseCode);
                
                // 尝试读取错误信息
                try {
                    BufferedReader errorReader = new BufferedReader(
                        new InputStreamReader(connection.getErrorStream()));
                    StringBuilder errorResponse = new StringBuilder();
                    String errorLine;
                    
                    while ((errorLine = errorReader.readLine()) != null) {
                        errorResponse.append(errorLine);
                    }
                    errorReader.close();
                    
                    if (errorResponse.length() > 0) {
                        System.out.println("    错误详情: " + errorResponse.toString());
                    }
                } catch (Exception e) {
                    // 忽略错误流读取异常
                }
            }
            
        } catch (java.net.SocketTimeoutException e) {
            System.out.println("  ❌ " + testName + " 失败 - 连接超时");
            System.out.println("    建议: 增加超时时间或检查网络连接");
            
        } catch (java.net.ConnectException e) {
            System.out.println("  ❌ " + testName + " 失败 - 连接被拒绝");
            System.out.println("    建议: 检查代理服务器是否运行");
            
        } catch (Exception e) {
            System.out.println("  ❌ " + testName + " 失败 - " + e.getMessage());
        }
    }
    
    /**
     * 测试健康检查端点
     */
    private static void testHealthEndpoint() {
        try {
            URL url = new URL(HEALTH_URL);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            
            connection.setRequestMethod("GET");
            connection.setRequestProperty("User-Agent", "PUMP-Monitor/1.0");
            connection.setConnectTimeout(5000);
            connection.setReadTimeout(10000);
            
            int responseCode = connection.getResponseCode();
            System.out.println("  健康检查 - 响应代码: " + responseCode);
            
            if (responseCode == 200) {
                System.out.println("  ✅ Jupiter API 服务正常");
            } else {
                System.out.println("  ⚠️ Jupiter API 服务状态异常");
            }
            
        } catch (Exception e) {
            System.out.println("  ❌ 健康检查失败 - " + e.getMessage());
        }
    }
}
