@echo off
chcp 65001 >nul
echo ========================================
echo 测试Java环境变量配置
echo ========================================
echo.

echo 临时设置环境变量进行测试...
set JAVA_HOME=F:\java-1.8.0
set PATH=%JAVA_HOME%\bin;%PATH%
echo.

echo 【测试1】检查JAVA_HOME设置：
echo JAVA_HOME = %JAVA_HOME%
echo.

echo 【测试2】检查Java版本：
java -version
echo.

echo 【测试3】检查Java编译器：
javac -version
echo.

echo 【测试4】检查Java安装路径：
where java
echo.

echo ========================================
echo 测试完成
echo ========================================
echo.

echo 如果以上所有测试都通过，说明Java环境配置正确！
echo 您应该使用以下配置：
echo   JAVA_HOME = F:\java-1.8.0
echo   PATH 中添加 = F:\java-1.8.0\bin
echo.

pause 