import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;

/**
 * 测试Jupiter Ultra API
 * 验证Ultra API是否正常工作
 * 
 * <AUTHOR> Agent
 * @version 1.0
 * @since 2025-01-15
 */
public class TestUltraAPI {
    
    private static final String PUMP_TOKEN = "pumpCmXqMfrsAkQ5r49WcJnRayYRqmXz6ae8H7H9Dfn";
    private static final String USDT_TOKEN = "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB";
    private static final String TAKER_ADDRESS = "778v7yvRRtW6YiHxZGhEjNsE8vwz3i9eSWtspxpiC1y7";
    
    public static void main(String[] args) {
        System.out.println("=== Jupiter Ultra API 测试 ===");
        System.out.println();
        
        // 测试买入价格（用$6000 USDT买PUMP）
        testBuyPrice();
        
        System.out.println();
        
        // 测试卖出价格（卖100万个PUMP得USDT）
        testSellPrice();
    }
    
    private static void testBuyPrice() {
        System.out.println("1. 测试买入价格（用$6000 USDT买PUMP）:");
        
        try {
            BigDecimal usdtAmount = new BigDecimal("6000");
            BigDecimal amountInSmallestUnit = usdtAmount.multiply(new BigDecimal("1000000"));
            
            String url = String.format(
                "https://lite-api.jup.ag/ultra/v1/order?inputMint=%s&outputMint=%s&amount=%s&swapMode=ExactIn&slippageBps=50" +
                "&broadcastFeeType=maxCap&priorityFeeLamports=1000000&useWsol=false" +
                "&asLegacyTransaction=false&excludeDexes=&excludeRouters=&taker=%s",
                USDT_TOKEN, PUMP_TOKEN, amountInSmallestUnit.toBigInteger(), TAKER_ADDRESS
            );
            
            System.out.println("请求URL: " + url);
            
            String response = makeHttpRequest(url);
            if (response != null) {
                System.out.println("响应: " + response);
                
                // 简单解析响应
                if (response.contains("\"inAmount\"") && response.contains("\"outAmount\"")) {
                    System.out.println("✅ Ultra API买入测试成功！");
                } else {
                    System.out.println("❌ Ultra API买入测试失败：响应格式不正确");
                }
            } else {
                System.out.println("❌ Ultra API买入测试失败：无响应");
            }
            
        } catch (Exception e) {
            System.out.println("❌ Ultra API买入测试异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void testSellPrice() {
        System.out.println("2. 测试卖出价格（卖100万个PUMP得USDT）:");
        
        try {
            BigDecimal pumpAmount = new BigDecimal("1000000");
            BigDecimal amountInSmallestUnit = pumpAmount.multiply(new BigDecimal("1000000"));
            
            String url = String.format(
                "https://lite-api.jup.ag/ultra/v1/order?inputMint=%s&outputMint=%s&amount=%s&swapMode=ExactIn&slippageBps=50" +
                "&broadcastFeeType=maxCap&priorityFeeLamports=1000000&useWsol=false" +
                "&asLegacyTransaction=false&excludeDexes=&excludeRouters=&taker=%s",
                PUMP_TOKEN, USDT_TOKEN, amountInSmallestUnit.toBigInteger(), TAKER_ADDRESS
            );
            
            System.out.println("请求URL: " + url);
            
            String response = makeHttpRequest(url);
            if (response != null) {
                System.out.println("响应: " + response);
                
                // 简单解析响应
                if (response.contains("\"inAmount\"") && response.contains("\"outAmount\"")) {
                    System.out.println("✅ Ultra API卖出测试成功！");
                } else {
                    System.out.println("❌ Ultra API卖出测试失败：响应格式不正确");
                }
            } else {
                System.out.println("❌ Ultra API卖出测试失败：无响应");
            }
            
        } catch (Exception e) {
            System.out.println("❌ Ultra API卖出测试异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static String makeHttpRequest(String urlString) {
        try {
            URL url = new URL(urlString);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(30000);
            connection.setReadTimeout(30000);
            connection.setRequestProperty("User-Agent", "PUMP-Monitor-Ultra-Test/1.0");
            connection.setRequestProperty("Accept", "application/json");
            
            int responseCode = connection.getResponseCode();
            System.out.println("响应状态码: " + responseCode);
            
            if (responseCode == 200) {
                try (BufferedReader reader = new BufferedReader(
                        new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8))) {
                    StringBuilder response = new StringBuilder();
                    String line;
                    while ((line = reader.readLine()) != null) {
                        response.append(line);
                    }
                    return response.toString();
                }
            } else {
                // 读取错误响应
                try (BufferedReader reader = new BufferedReader(
                        new InputStreamReader(connection.getErrorStream(), StandardCharsets.UTF_8))) {
                    StringBuilder errorResponse = new StringBuilder();
                    String line;
                    while ((line = reader.readLine()) != null) {
                        errorResponse.append(line);
                    }
                    System.out.println("错误响应: " + errorResponse.toString());
                }
                return null;
            }
            
        } catch (Exception e) {
            System.out.println("HTTP请求异常: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }
}
