# 🚨 PUMP监控系统报警问题诊断与修复

## 📋 问题分析

### **您遇到的问题**：
```
报警没有了
Jupiter Ultra API请求失败，状态码: 429, 错误信息: Rate limit exceeded
SSLHandshakeException: Remote host terminated the handshake
```

## 🔍 根本原因

### 1. **API调用频率分析（修正）**
- **每次监控调用**：
  - Gate.io API: 1次
  - Jupiter Ultra API: 2次（买入 + 卖出）
- **1.8秒间隔频率**：(60÷1.8) × 2 = **66.67次Jupiter API/分钟**
- **结果**：超过免费版60次/分钟限制 ❌

### 2. **SSL连接问题**
- 网络不稳定或代理配置问题
- 导致API调用失败，无法获取价格数据

### 3. **报警触发失败**
- 由于API失败，无法计算有效差价
- 报警条件不满足，不会播放音效

## ✅ 修复方案

### **1. 调整监控间隔**
```properties
# 修改前
pump.monitor.interval=1800  # 1.8秒 → 66.67次/分钟 ❌

# 修改后  
pump.monitor.interval=2000  # 2秒 → 60次/分钟 ✅
```

**计算验证**：
- 每分钟监控次数：60÷2 = 30次
- Jupiter API调用：30 × 2 = 60次/分钟
- 结果：正好符合免费版限制 ✅

### **2. 添加API重试机制**
```java
// 处理429限流错误
if (responseCode == 429) {
    logger.warn("Jupiter API限流，等待后重试...");
    Thread.sleep(2000); // 等待2秒
    connection = createConnection(url);
    responseCode = connection.getResponseCode();
}
```

### **3. 报警恢复预期**
修复后，当满足以下条件时会触发报警：
- ✅ API调用成功获取价格数据
- ✅ 计算出有效的价格差异
- ✅ 差价超过$30阈值
- ✅ 播放自定义音频文件

## 🎯 验证步骤

### **1. 重新启动系统**
```bash
# 停止当前系统
Ctrl+C

# 重新启动
java -jar target/pump-monitor-1.0.jar
```

### **2. 观察日志输出**
**正常输出应该是**：
```
PUMP价格监控系统已启动，监控间隔: 2000ms
报警配置 - 启用: true, 买入阈值: $30.00, 卖出阈值: $30.00, 冷却时间: 30s, 音频类型: CUSTOM

2025-07-15 12:00:00.000 : ===============PUMP监控 ===============GATE单价: $5550.00
2025-07-15 12:00:00.000 : 池买入100W个PUMP: $5564.03，差价：$-14.03，做升
2025-07-15 12:00:00.000 : 池卖出100W个PUMP: $5555.63，差价：$5.63，做跌
```

### **3. 报警测试**
当差价超过$30时，应该：
- ✅ 播放自定义音频（buy_alert.wav 或 sell_alert.wav）
- ✅ 不显示额外的控制台信息
- ✅ 遵守30秒冷却时间

## 🔧 进一步优化建议

### **如果仍有429错误**
1. **增加监控间隔**：
   ```properties
   pump.monitor.interval=2500  # 2.5秒 → 48次/分钟
   ```

2. **考虑升级到付费版本**：
   - Pro I: 600次/分钟 → 可以用0.2秒间隔
   - 更稳定的服务质量

### **如果SSL问题持续**
1. **检查代理设置**：
   ```properties
   proxy.enabled=true
   proxy.host=127.0.0.1
   proxy.port=7890
   proxy.type=SOCKS
   ```

2. **网络诊断**：
   ```bash
   # 测试网络连接
   curl -x socks5://127.0.0.1:7890 https://lite-api.jup.ag/
   ```

## 📊 监控频率对比表

| 间隔 | 每分钟监控 | Jupiter调用 | 状态 | 适用场景 |
|------|------------|-------------|------|----------|
| 1.8秒 | 33.33次 | 66.67次 | ❌ 超限 | - |
| 2.0秒 | 30次 | 60次 | ✅ 极限 | 免费版最快 |
| 2.5秒 | 24次 | 48次 | ✅ 安全 | 免费版推荐 |
| 3.0秒 | 20次 | 40次 | ✅ 保守 | 稳定监控 |

## 🎵 自定义音频状态

**当前配置**：
- ✅ 音频文件已生成：`sounds/buy_alert.wav`, `sounds/sell_alert.wav`
- ✅ 配置已启用：`pump.alert.sound-type=CUSTOM`
- ✅ 阈值设置：买入$30, 卖出$30
- ✅ 冷却时间：30秒

**预期效果**：
- 买入机会：播放800Hz，2声短音
- 卖出机会：播放1200Hz，3声清音

## 🚀 总结

**修复内容**：
1. ✅ 调整监控间隔：1.8秒 → 2秒
2. ✅ 添加429错误重试机制
3. ✅ 保持自定义音频配置

**预期结果**：
- 不再出现429限流错误
- API调用成功，获取完整价格数据
- 报警功能恢复正常
- 差价超过$30时播放自定义音效

现在重新启动系统，报警功能应该恢复正常了！🎉
