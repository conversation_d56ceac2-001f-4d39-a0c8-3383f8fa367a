import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.Proxy;
import java.net.InetSocketAddress;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 测试Jupiter Quote API买入和卖出价格差异
 * 验证买入和卖出是否有正确的价差
 * 
 * <AUTHOR> Agent
 * @version 1.0
 * @since 2025-01-15
 */
public class TestBuySellPriceDifference {
    
    // 正确的代币地址
    private static final String PUMP_TOKEN = "pumpCmXqMfrsAkQ5r49WcJnRayYRqmXz6ae8H7H9Dfn";
    private static final String USDT_TOKEN = "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB";
    
    public static void main(String[] args) {
        System.out.println("=== 测试Jupiter Quote API买入和卖出价格差异 ===");
        System.out.println();
        
        BigDecimal pumpAmount = new BigDecimal("1000000"); // 100万个PUMP
        
        // 测试卖出价格（PUMP -> USDT）
        System.out.println("1. 测试卖出价格（PUMP -> USDT）");
        BigDecimal sellPrice = testSellPrice(pumpAmount);
        
        System.out.println();
        
        // 测试买入价格（USDT -> PUMP）
        System.out.println("2. 测试买入价格（USDT -> PUMP）");
        BigDecimal buyPrice = testBuyPrice(pumpAmount, sellPrice);
        
        System.out.println();
        
        // 比较价格差异
        if (sellPrice != null && buyPrice != null) {
            comparePrices(sellPrice, buyPrice, pumpAmount);
        }
        
        System.out.println("\n=== 测试完成 ===");
    }
    
    /**
     * 测试卖出价格
     */
    private static BigDecimal testSellPrice(BigDecimal pumpAmount) {
        try {
            // 卖出：PUMP -> USDT
            String inputMint = PUMP_TOKEN;
            String outputMint = USDT_TOKEN;
            BigDecimal amountInSmallestUnit = pumpAmount.multiply(new BigDecimal("1000000"));
            
            String url = String.format("https://lite-api.jup.ag/swap/v1/quote?inputMint=%s&outputMint=%s&amount=%s&slippageBps=50&swapMode=ExactIn",
                                     inputMint, outputMint, amountInSmallestUnit.toBigInteger());
            
            System.out.println("卖出请求URL: " + url);
            
            String responseBody = sendRequest(url);
            if (responseBody != null) {
                return parseSellResponse(responseBody, pumpAmount);
            }
            
        } catch (Exception e) {
            System.out.println("❌ 卖出测试失败: " + e.getMessage());
        }
        return null;
    }
    
    /**
     * 测试买入价格
     */
    private static BigDecimal testBuyPrice(BigDecimal pumpAmount, BigDecimal estimatedSellPrice) {
        try {
            // 买入：USDT -> PUMP
            // 估算需要的USDT数量
            BigDecimal estimatedUsdtNeeded = pumpAmount.multiply(estimatedSellPrice != null ? estimatedSellPrice : new BigDecimal("0.006"));
            estimatedUsdtNeeded = estimatedUsdtNeeded.multiply(new BigDecimal("1.1")); // 多估算10%
            
            String inputMint = USDT_TOKEN;
            String outputMint = PUMP_TOKEN;
            BigDecimal amountInSmallestUnit = estimatedUsdtNeeded.multiply(new BigDecimal("1000000"));
            
            String url = String.format("https://lite-api.jup.ag/swap/v1/quote?inputMint=%s&outputMint=%s&amount=%s&slippageBps=50&swapMode=ExactIn",
                                     inputMint, outputMint, amountInSmallestUnit.toBigInteger());
            
            System.out.println("买入请求URL: " + url);
            System.out.println("估算USDT需求: " + estimatedUsdtNeeded);
            
            String responseBody = sendRequest(url);
            if (responseBody != null) {
                return parseBuyResponse(responseBody, pumpAmount);
            }
            
        } catch (Exception e) {
            System.out.println("❌ 买入测试失败: " + e.getMessage());
        }
        return null;
    }
    
    /**
     * 发送HTTP请求
     */
    private static String sendRequest(String urlString) {
        try {
            URL url = new URL(urlString);
            Proxy proxy = new Proxy(Proxy.Type.SOCKS, new InetSocketAddress("127.0.0.1", 7890));
            HttpURLConnection connection = (HttpURLConnection) url.openConnection(proxy);
            
            connection.setRequestMethod("GET");
            connection.setRequestProperty("User-Agent", "PUMP-Monitor/1.0");
            connection.setRequestProperty("Accept", "application/json");
            connection.setConnectTimeout(15000);
            connection.setReadTimeout(20000);
            
            int responseCode = connection.getResponseCode();
            System.out.println("响应代码: " + responseCode);
            
            if (responseCode == 200) {
                BufferedReader reader = new BufferedReader(
                    new InputStreamReader(connection.getInputStream()));
                StringBuilder response = new StringBuilder();
                String line;
                
                while ((line = reader.readLine()) != null) {
                    response.append(line);
                }
                reader.close();
                
                return response.toString();
            } else {
                System.out.println("❌ 请求失败，响应代码: " + responseCode);
            }
            
        } catch (Exception e) {
            System.out.println("❌ 请求异常: " + e.getMessage());
        }
        return null;
    }
    
    /**
     * 解析卖出响应
     */
    private static BigDecimal parseSellResponse(String responseBody, BigDecimal pumpAmount) {
        try {
            System.out.println("卖出响应: " + responseBody.substring(0, Math.min(200, responseBody.length())) + "...");
            
            // 简单JSON解析
            String inAmountKey = "\"inAmount\":\"";
            String outAmountKey = "\"outAmount\":\"";
            
            int inStart = responseBody.indexOf(inAmountKey) + inAmountKey.length();
            int inEnd = responseBody.indexOf("\"", inStart);
            String inAmountStr = responseBody.substring(inStart, inEnd);
            
            int outStart = responseBody.indexOf(outAmountKey) + outAmountKey.length();
            int outEnd = responseBody.indexOf("\"", outStart);
            String outAmountStr = responseBody.substring(outStart, outEnd);
            
            BigDecimal inAmount = new BigDecimal(inAmountStr);  // PUMP最小单位
            BigDecimal outAmount = new BigDecimal(outAmountStr); // USDT最小单位
            
            // 转换为正常单位
            BigDecimal pumpNormal = inAmount.divide(new BigDecimal("1000000"), 8, RoundingMode.HALF_UP);
            BigDecimal usdtNormal = outAmount.divide(new BigDecimal("1000000"), 8, RoundingMode.HALF_UP);
            
            // 卖出单价 = 输出USDT / 输入PUMP
            BigDecimal sellUnitPrice = usdtNormal.divide(pumpNormal, 8, RoundingMode.HALF_UP);
            
            System.out.println("卖出解析: " + pumpNormal + "PUMP -> " + usdtNormal + "USDT");
            System.out.println("卖出单价: " + sellUnitPrice + " USDT/PUMP");
            System.out.println("100万个PUMP卖出总价: " + sellUnitPrice.multiply(pumpAmount) + " USDT");
            
            return sellUnitPrice;
            
        } catch (Exception e) {
            System.out.println("❌ 卖出响应解析失败: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 解析买入响应
     */
    private static BigDecimal parseBuyResponse(String responseBody, BigDecimal pumpAmount) {
        try {
            System.out.println("买入响应: " + responseBody.substring(0, Math.min(200, responseBody.length())) + "...");
            
            // 简单JSON解析
            String inAmountKey = "\"inAmount\":\"";
            String outAmountKey = "\"outAmount\":\"";
            
            int inStart = responseBody.indexOf(inAmountKey) + inAmountKey.length();
            int inEnd = responseBody.indexOf("\"", inStart);
            String inAmountStr = responseBody.substring(inStart, inEnd);
            
            int outStart = responseBody.indexOf(outAmountKey) + outAmountKey.length();
            int outEnd = responseBody.indexOf("\"", outStart);
            String outAmountStr = responseBody.substring(outStart, outEnd);
            
            BigDecimal inAmount = new BigDecimal(inAmountStr);  // USDT最小单位
            BigDecimal outAmount = new BigDecimal(outAmountStr); // PUMP最小单位
            
            // 转换为正常单位
            BigDecimal usdtNormal = inAmount.divide(new BigDecimal("1000000"), 8, RoundingMode.HALF_UP);
            BigDecimal pumpNormal = outAmount.divide(new BigDecimal("1000000"), 8, RoundingMode.HALF_UP);
            
            // 买入单价 = 输入USDT / 输出PUMP
            BigDecimal buyUnitPrice = usdtNormal.divide(pumpNormal, 8, RoundingMode.HALF_UP);
            
            System.out.println("买入解析: " + usdtNormal + "USDT -> " + pumpNormal + "PUMP");
            System.out.println("买入单价: " + buyUnitPrice + " USDT/PUMP");
            System.out.println("100万个PUMP买入总价: " + buyUnitPrice.multiply(pumpAmount) + " USDT");
            
            return buyUnitPrice;
            
        } catch (Exception e) {
            System.out.println("❌ 买入响应解析失败: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 比较买入和卖出价格
     */
    private static void comparePrices(BigDecimal sellPrice, BigDecimal buyPrice, BigDecimal pumpAmount) {
        System.out.println("=== 价格比较分析 ===");
        
        BigDecimal sellTotal = sellPrice.multiply(pumpAmount);
        BigDecimal buyTotal = buyPrice.multiply(pumpAmount);
        BigDecimal priceDiff = buyPrice.subtract(sellPrice);
        BigDecimal totalDiff = buyTotal.subtract(sellTotal);
        
        System.out.println("卖出单价: " + String.format("%.8f", sellPrice) + " USDT/PUMP");
        System.out.println("买入单价: " + String.format("%.8f", buyPrice) + " USDT/PUMP");
        System.out.println("单价差异: " + String.format("%.8f", priceDiff) + " USDT/PUMP");
        System.out.println();
        System.out.println("100万个PUMP卖出总价: " + String.format("%.2f", sellTotal) + " USDT");
        System.out.println("100万个PUMP买入总价: " + String.format("%.2f", buyTotal) + " USDT");
        System.out.println("总价差异: " + String.format("%.2f", totalDiff) + " USDT");
        
        // 验证是否有合理的价差
        if (priceDiff.compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal spreadPercent = priceDiff.divide(sellPrice, 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
            System.out.println("价差百分比: " + String.format("%.4f", spreadPercent) + "%");
            System.out.println("✅ 买入价格高于卖出价格，符合市场逻辑");
        } else {
            System.out.println("❌ 价格异常：买入价格应该高于卖出价格");
        }
    }
}
