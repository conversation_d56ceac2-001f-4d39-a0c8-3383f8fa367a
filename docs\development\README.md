# PUMP价格监控系统 - 开发者指南

## 📋 开发前必读

> **重要**: 在开始任何开发工作之前，开发者**必须**先阅读 `docs/` 目录下的现有文档，以了解当前项目状态并获取必要的上下文。

## 📁 文档组织结构

所有文档文件**必须**放置在 `docs/` 目录中，并按照以下结构组织：

```
docs/
├── development/              # 开发指南和规范
│   ├── README.md            # 本文档 - 开发者指南
│   ├── api-research/        # API调研和分析
│   └── architecture/        # 架构设计文档
├── delivery/                # 项目交付文档
│   ├── backlog.md          # 产品待办事项
│   ├── README.md           # 项目概览
│   └── PBI-001/            # 具体PBI详细文档
│       ├── prd.md          # 产品需求文档
│       ├── tasks.md        # 任务列表
│       └── *.md            # 具体任务文档
└── technical/               # 技术文档
    ├── architecture.md     # 系统架构
    ├── api-integration/    # API集成文档
    └── troubleshooting/    # 故障排除指南
```

## 📖 开发前阅读清单

### 1. 项目概览 (必读)
- **文档**: `docs/delivery/README.md`
- **内容**: 项目简介、核心功能、技术栈
- **目的**: 理解项目整体目标和范围

### 2. 产品需求 (必读)
- **文档**: `docs/delivery/PBI-001/prd.md`
- **内容**: 详细的产品需求文档，包括用户故事、技术要求、验收标准
- **目的**: 理解具体功能需求和业务逻辑

### 3. 技术架构 (必读)
- **文档**: `docs/technical/architecture.md`
- **内容**: 系统架构设计、技术选型、组件关系
- **目的**: 理解系统设计和技术实现方案

### 4. 任务分解 (必读)
- **文档**: `docs/delivery/PBI-001/tasks.md`
- **内容**: 具体开发任务列表和状态
- **目的**: 了解当前开发进度和待办任务

### 5. API集成文档 (推荐)
- **文档**: `docs/technical/api-integration/`
- **内容**: 第三方API集成指南和最佳实践
- **目的**: 理解API使用方式和注意事项

## 🎯 当前项目状态

### 核心功能
- **监控目标**: PUMP代币价格监控
- **交易平台**: 
  - CEX: Gate.io (https://www.gate.com/zh/trade/PUMP_USDT)
  - DEX: Jupiter Aggregator
- **交易对**: PUMP/USDT
- **交易量**: 100,000 PUMP (10万个)
- **监控间隔**: 2秒

### 技术栈
- **框架**: Spring Boot 2.1.1
- **语言**: Java 8
- **构建工具**: Maven
- **API客户端**: RestTemplate + Jackson
- **调度器**: Spring Scheduler

### 输出格式
```
2025-01-15 14:30:25.123  : ===============PUMP监控 ===============
2025-01-15 14:30:25.123  : 池买入10W个PUMP: 3022.00个USDT，差价：-17.04，做升
2025-01-15 14:30:25.123  : 池卖出10W个PUMP: 3039.04个USDT，差价：-5.16  ，做跌
```

## 🔧 开发环境要求

### 必需软件
- Java 8 或更高版本
- Maven 3.x
- IDE (推荐: IntelliJ IDEA 或 Eclipse)

### 项目配置
- **配置文件**: `src/main/resources/application.properties`
- **核心参数**:
  - `pump.monitor.interval=2000` (监控间隔2秒)
  - `pump.monitor.amount=100000` (交易量10万个PUMP)
  - `gate.api.timeout=5000` (Gate.io API超时5秒)
  - `jupiter.api.timeout=5000` (Jupiter API超时5秒)

## 🚨 关键问题和注意事项

### 价格选择策略问题 (高优先级)
- **问题**: 当前系统错误地把买入价当做卖出价
- **根因**: Jupiter API使用不当，仅基于高低价对比选择价格
- **影响**: DEX的买入/卖出价差较大，影响套利分析准确性
- **解决方案**: 需要使用Jupiter的专门买入/卖出API端点

### API集成要点
- **Gate.io**: 使用REST API获取实时价格
- **Jupiter**: 需要区分Price API和Quote API
  - Price API: 获取参考价格
  - Quote API: 获取具体交易报价

## 📝 开发规范

### 代码注释
- 所有函数必须包含 **JSDoc 或 TSDoc** 注释
- 模块/组件/类文件开头必须有功能说明
- 所有HTTP接口必须使用Swagger-style注释

### 错误处理
- 完善的异常捕获和处理机制
- 详细的日志记录
- 优雅的错误恢复

### 配置管理
- 参数外部化配置
- 支持不同环境配置
- 敏感信息安全存储

## 🎯 下一步开发重点

1. **修正Jupiter API集成** (高优先级)
   - 调研Jupiter API V2的正确端点
   - 实现买入/卖出价格分离获取
   - 优化价格选择策略

2. **完善错误处理**
   - API调用失败的重试机制
   - 网络异常的恢复策略
   - 数据异常的处理逻辑

3. **性能优化**
   - API调用的并发处理
   - 缓存机制的实现
   - 监控性能的提升

## 🔗 有用的链接

- **Jupiter API文档**: https://dev.jup.ag/docs/
- **Gate.io API文档**: https://www.gate.io/docs/developers/
- **Spring Boot文档**: https://spring.io/projects/spring-boot
- **项目Git仓库**: 当前目录

---

**文档版本**: 1.0  
**最后更新**: 2025-01-15  
**维护者**: AI Agent  

> 💡 **提示**: 如果您发现文档中有任何错误或需要补充的内容，请及时更新相关文档并通知团队成员。 