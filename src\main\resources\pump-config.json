{"monitor": {"interval": 200, "amount": 1000000, "comment": "监控间隔(毫秒)和监控数量(PUMP代币数量)"}, "alert": {"enabled": true, "buyThreshold": 1.0, "sellThreshold": 1.0, "soundType": "CUSTOM", "continuousPlay": true, "comment": "报警配置 - 阈值单位为美元，soundType可选: SYSTEM, MULTIPLE, CUSTOM，continuousPlay控制是否连续播放"}, "api": {"timeout": 30000, "retryDelay": 2000, "comment": "API配置 - 超时时间和重试延迟(毫秒)"}, "proxy": {"enabled": false, "host": "127.0.0.1", "port": 7890, "type": "SOCKS", "comment": "代理配置 - type可选: HTTP, SOCKS"}}