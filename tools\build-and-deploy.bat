@echo off
chcp 65001 >nul
title PUMP价格监控系统 - 构建和部署脚本

echo ===============================================================================
echo 🚀 PUMP价格监控系统 - 构建和部署脚本
echo ===============================================================================
echo.

echo 📝 步骤1：修改报警阈值配置
echo ───────────────────────────────────────────────────────────────────────────────

:: 获取当前日期 (确保纯数字格式 yyyy-MM-dd)
for /f %%i in ('powershell -command "Get-Date -format yyyy-MM-dd"') do set datetime=%%i

# 获取当前配置（使用PowerShell解析JSON）
for /f "tokens=*" %%i in ('powershell -Command "& {$config = Get-Content 'src\main\resources\pump-config.json' -Encoding UTF8 | ConvertFrom-Json; Write-Host $config.alert.buyThreshold}"') do set currentBuyThreshold=%%i
for /f "tokens=*" %%i in ('powershell -Command "& {$config = Get-Content 'src\main\resources\pump-config.json' -Encoding UTF8 | ConvertFrom-Json; Write-Host $config.alert.sellThreshold}"') do set currentSellThreshold=%%i
for /f "tokens=*" %%i in ('powershell -Command "& {$config = Get-Content 'src\main\resources\pump-config.json' -Encoding UTF8 | ConvertFrom-Json; Write-Host $config.monitor.amount}"') do set currentMonitorAmount=%%i

echo.
echo =====================================================
echo 📊 当前配置：
echo    💰 买入阈值：%currentBuyThreshold% 美元
echo    💸 卖出阈值：%currentSellThreshold% 美元
echo    📈 监控数量：%currentMonitorAmount% 个PUMP
echo =====================================================
echo.

:: 输入新配置
echo 🔧 请输入新的监控配置：
echo.
set /p newBuyThreshold=💰 买入阈值 (美元，当前：%currentBuyThreshold%): 
if "%newBuyThreshold%"=="" set newBuyThreshold=%currentBuyThreshold%

set /p newSellThreshold=💸 卖出阈值 (美元，当前：%currentSellThreshold%): 
if "%newSellThreshold%"=="" set newSellThreshold=%currentSellThreshold%

set /p newMonitorAmount=📈 监控数量 (个PUMP，当前：%currentMonitorAmount%): 
if "%newMonitorAmount%"=="" set newMonitorAmount=%currentMonitorAmount%

echo.
echo ✅ 新配置确认：
echo    💰 买入阈值：%newBuyThreshold% 美元
echo    💸 卖出阈值：%newSellThreshold% 美元  
echo    📈 监控数量：%newMonitorAmount% 个PUMP
echo.
set /p confirm=📝 确认修改配置？(Y/n): 
if /i "%confirm%"=="n" (
    echo ❌ 用户取消操作
    exit /b 1
)

:: 更新配置文件（使用PowerShell）
powershell -Command "& {$config = Get-Content 'src\main\resources\pump-config.json' -Encoding UTF8 | ConvertFrom-Json; $config.alert.buyThreshold = [double]'%newBuyThreshold%'; $config.alert.sellThreshold = [double]'%newSellThreshold%'; $config.monitor.amount = [long]'%newMonitorAmount%'; $config | ConvertTo-Json -Depth 5 | Set-Content 'src\main\resources\pump-config.json' -Encoding UTF8}"

if %errorlevel% neq 0 (
    echo ❌ 更新配置文件失败
    exit /b 1
)
echo ✅ 配置文件已更新

echo.
echo ✅ 配置信息：
echo    💰 买入阈值：%buyThreshold% 美元
echo    💸 卖出阈值：%sellThreshold% 美元
echo.

pause

echo 🔨 步骤2：编译打包JAR
echo ───────────────────────────────────────────────────────────────────────────────

echo 🧹 清理target目录...
call mvn clean >nul

echo 🔨 开始编译和打包...
call mvn clean package -DskipTests

if %errorlevel% neq 0 (
    echo ❌ JAR包构建失败
    pause
    exit /b 1
)

echo ✅ JAR包构建成功
echo.

echo 📁 步骤3：创建打包记录目录并复制JAR包
echo ───────────────────────────────────────────────────────────────────────────────

:: 创建builds目录结构
set buildDir=builds
set targetDir=%buildDir%\%datetime%
if not exist %buildDir% mkdir %buildDir%
if not exist %targetDir% mkdir %targetDir%

:: 生成包名（智能命名：相同阈值使用简化名称）
if "%buyThreshold%"=="%sellThreshold%" (
    set packageName=pump%buyThreshold%.jar
) else (
    set packageName=pump_buy%buyThreshold%_sell%sellThreshold%.jar
)
set targetJarPath=%targetDir%\%packageName%

:: 复制JAR包（如果存在则覆盖）
if exist "%targetJarPath%" (
    echo ⚠️  文件已存在，将被覆盖：%packageName%
)
copy target\pump.jar "%targetJarPath%" >nul
echo 📦 JAR包已复制：%targetJarPath%
echo.

echo 🚀 步骤4：生成启动脚本
echo ───────────────────────────────────────────────────────────────────────────────

:: 生成启动脚本
if "%buyThreshold%"=="%sellThreshold%" (
    set scriptName=start_pump%buyThreshold%.bat
    set thresholdDisplay=阈值:%buyThreshold%美元
) else (
    set scriptName=start_pump_buy%buyThreshold%_sell%sellThreshold%.bat
    set thresholdDisplay=买入:%buyThreshold%美元 卖出:%sellThreshold%美元
)
set scriptPath=%targetDir%\%scriptName%

(
echo @echo off
echo chcp 65001 ^>nul
echo title PUMP价格监控系统 - %thresholdDisplay%
echo cls
echo.
echo ===============================================================================
echo 🚀 PUMP价格监控系统
echo ===============================================================================
echo 📊 配置信息：
echo    💰 买入阈值：%buyThreshold% 美元
echo    💸 卖出阈值：%sellThreshold% 美元
echo    📦 JAR包：%packageName%
echo    📅 构建日期：%datetime%
echo    📅 构建时间：%date% %time%
echo ===============================================================================
echo.
echo 🔄 启动中...
echo java -Dfile.encoding=UTF-8 -Dconsole.encoding=UTF-8 -Duser.timezone=Asia/Shanghai -jar "%packageName%"
echo.
echo ===============================================================================
echo 程序已退出，按任意键关闭窗口...
echo pause ^>nul
) > "%scriptPath%"

if exist "%scriptPath%" (
    echo ⚠️  启动脚本已存在，将被覆盖：%scriptName%
)
echo 🚀 启动脚本已生成：%scriptPath%

:: 生成说明文件
set readmePath=%targetDir%\README.txt
(
echo ===============================================================================
echo PUMP价格监控系统 - 构建包 %datetime%
echo ===============================================================================
echo.
echo 📦 包内容：
echo - %packageName%           # 可执行JAR包 ^(%thresholdDisplay%^)
echo - %scriptName%            # 启动脚本
echo - README.txt                      # 本说明文件
echo.
echo ⚙️ 配置信息：
echo - 💰 买入阈值：%buyThreshold% 美元
echo - 💸 卖出阈值：%sellThreshold% 美元
echo - 📅 构建日期：%datetime%
echo - 📅 构建时间：%date% %time%
echo.
echo 🚀 使用方法：
echo 1. 双击 %scriptName% 启动程序
echo 2. 或者使用命令行：java -jar "%packageName%"
echo.
echo 📋 系统要求：
echo - Java 8 或以上版本
echo - Windows 系统
echo.
echo 💡 文件命名规则：
echo - 相同阈值：pump{阈值}.jar ^(如：pump1.5.jar^)
echo - 不同阈值：pump_buy{买入}_sell{卖出}.jar
echo - 同名文件将被自动覆盖
echo.
echo ===============================================================================
echo 构建脚本：tools\build-and-deploy.bat
echo ===============================================================================
) > "%readmePath%"

echo 📋 说明文件已生成：%readmePath%
echo.

echo ===============================================================================
echo 🎉 构建和部署完成！
echo ===============================================================================
echo.
echo 📁 构建目录：%targetDir%
echo 📦 JAR包：%packageName%
echo 🚀 启动脚本：%scriptName%
echo.
echo ⚙️ 配置信息：
echo    💰 买入阈值：%buyThreshold% 美元
echo    💸 卖出阈值：%sellThreshold% 美元
echo.

set /p launchNow=🚀 是否立即启动程序？(Y/n): 
if /i "%launchNow%" neq "n" (
    echo.
    echo 🚀 启动程序...
    cd /d %targetDir%
    start %scriptName%
    echo ✅ 程序已在新窗口中启动
)

echo.
echo ===============================================================================
echo 🎯 任务完成！感谢使用PUMP价格监控系统构建脚本
echo ===============================================================================
pause 