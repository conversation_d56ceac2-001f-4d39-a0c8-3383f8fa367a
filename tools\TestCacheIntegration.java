import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 测试缓存集成功能
 * 
 * <AUTHOR> Agent
 * @version 1.0
 * @since 2025-01-15
 */
public class TestCacheIntegration {
    
    public static void main(String[] args) {
        System.out.println("=== 缓存机制集成测试 ===");
        System.out.println();
        
        // 创建缓存实例
        TestPriceCache cache = new TestPriceCache();
        
        // 测试1: 基本缓存功能
        System.out.println("1. 测试基本缓存功能...");
        TestPriceData testData = new TestPriceData(new BigDecimal("0.00001234"));
        
        // 存储数据
        cache.put("test:price", testData);
        
        // 立即获取
        TestPriceData retrieved = cache.get("test:price");
        if (retrieved != null && retrieved.getPrice().equals(testData.getPrice())) {
            System.out.println("✅ 缓存存储和获取成功");
        } else {
            System.out.println("❌ 缓存存储和获取失败");
        }
        
        // 测试2: 缓存过期
        System.out.println("\n2. 测试缓存过期...");
        try {
            Thread.sleep(2000); // 等待2秒
            TestPriceData expiredData = cache.get("test:price");
            if (expiredData == null) {
                System.out.println("✅ 缓存正确过期");
            } else {
                System.out.println("❌ 缓存未正确过期");
            }
        } catch (InterruptedException e) {
            System.out.println("测试中断");
        }
        
        // 测试3: 过期数据获取
        System.out.println("\n3. 测试过期数据获取...");
        TestPriceData staleData = cache.getStale("test:price");
        if (staleData != null && staleData.getPrice().equals(testData.getPrice())) {
            System.out.println("✅ 过期数据获取成功");
        } else {
            System.out.println("❌ 过期数据获取失败");
        }
        
        // 测试4: 缓存统计
        System.out.println("\n4. 测试缓存统计...");
        cache.put("test:price2", new TestPriceData(new BigDecimal("0.00002345")));
        TestCacheStats stats = cache.getStats();
        System.out.println("缓存统计: " + stats);
        
        // 测试5: 缓存清理
        System.out.println("\n5. 测试缓存清理...");
        cache.clear();
        TestCacheStats statsAfterClear = cache.getStats();
        if (statsAfterClear.getTotalEntries() == 0) {
            System.out.println("✅ 缓存清理成功");
        } else {
            System.out.println("❌ 缓存清理失败");
        }
        
        System.out.println("\n=== 测试完成 ===");
    }
    
    /**
     * 简化的价格缓存实现（用于测试）
     */
    static class TestPriceCache {
        private final java.util.concurrent.ConcurrentHashMap<String, TestCacheEntry> cache = 
            new java.util.concurrent.ConcurrentHashMap<>();
        
        private static final long CACHE_TTL_SECONDS = 1; // 1秒过期（用于测试）
        
        public TestPriceData get(String key) {
            TestCacheEntry entry = cache.get(key);
            if (entry != null && isValid(entry)) {
                return entry.getData();
            }
            return null;
        }
        
        public void put(String key, TestPriceData data) {
            if (data != null && data.isValid()) {
                TestCacheEntry entry = new TestCacheEntry(data, LocalDateTime.now());
                cache.put(key, entry);
            }
        }
        
        public TestPriceData getStale(String key) {
            TestCacheEntry entry = cache.get(key);
            return entry != null ? entry.getData() : null;
        }
        
        public void clear() {
            cache.clear();
        }
        
        public TestCacheStats getStats() {
            int total = cache.size();
            int valid = 0;
            int expired = 0;
            
            for (TestCacheEntry entry : cache.values()) {
                if (isValid(entry)) {
                    valid++;
                } else {
                    expired++;
                }
            }
            
            return new TestCacheStats(total, valid, expired);
        }
        
        private boolean isValid(TestCacheEntry entry) {
            LocalDateTime now = LocalDateTime.now();
            long secondsSinceCreation = java.time.temporal.ChronoUnit.SECONDS.between(
                entry.getCreatedAt(), now);
            return secondsSinceCreation <= CACHE_TTL_SECONDS;
        }
    }
    
    /**
     * 缓存项
     */
    static class TestCacheEntry {
        private final TestPriceData data;
        private final LocalDateTime createdAt;
        
        public TestCacheEntry(TestPriceData data, LocalDateTime createdAt) {
            this.data = data;
            this.createdAt = createdAt;
        }
        
        public TestPriceData getData() {
            return data;
        }
        
        public LocalDateTime getCreatedAt() {
            return createdAt;
        }
    }
    
    /**
     * 简化的价格数据类
     */
    static class TestPriceData {
        private BigDecimal price;
        private String errorMessage;
        
        public TestPriceData(BigDecimal price) {
            this.price = price;
        }
        
        public boolean isValid() {
            return price != null && price.compareTo(BigDecimal.ZERO) > 0;
        }
        
        public BigDecimal getPrice() {
            return price;
        }
        
        public String getErrorMessage() {
            return errorMessage;
        }
        
        public void setErrorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
        }
    }
    
    /**
     * 缓存统计信息
     */
    static class TestCacheStats {
        private final int totalEntries;
        private final int validEntries;
        private final int expiredEntries;
        
        public TestCacheStats(int totalEntries, int validEntries, int expiredEntries) {
            this.totalEntries = totalEntries;
            this.validEntries = validEntries;
            this.expiredEntries = expiredEntries;
        }
        
        public int getTotalEntries() {
            return totalEntries;
        }
        
        public int getValidEntries() {
            return validEntries;
        }
        
        public int getExpiredEntries() {
            return expiredEntries;
        }
        
        @Override
        public String toString() {
            return String.format("总计=%d, 有效=%d, 过期=%d", 
                               totalEntries, validEntries, expiredEntries);
        }
    }
}
