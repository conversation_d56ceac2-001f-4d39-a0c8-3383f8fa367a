# PUMP30 价格监控系统 UTF-8 启动脚本
Write-Host "================================================" -ForegroundColor Green
Write-Host "           PUMP30 价格监控系统 (UTF-8)" -ForegroundColor Green  
Write-Host "================================================" -ForegroundColor Green
Write-Host ""

# 设置控制台编码为UTF-8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
[Console]::InputEncoding = [System.Text.Encoding]::UTF8

# 设置控制台代码页
try {
    chcp 65001 | Out-Null
    Write-Host "[信息] 控制台编码已设置为UTF-8" -ForegroundColor Yellow
} catch {
    Write-Host "[警告] 无法设置控制台代码页" -ForegroundColor Red
}

Write-Host "[信息] 正在启动PUMP30价格监控系统..." -ForegroundColor Yellow
Write-Host "[信息] 按 Ctrl+C 停止程序" -ForegroundColor Yellow
Write-Host ""

# 检查JAR文件是否存在
if (-not (Test-Path "pump30.jar")) {
    Write-Host "[错误] pump30.jar 文件不存在!" -ForegroundColor Red
    Read-Host "按回车键退出"
    exit 1
}

# 启动Java程序with UTF-8 参数
try {
    java -Dfile.encoding=UTF-8 -Dconsole.encoding=UTF-8 -Dsun.jnu.encoding=UTF-8 -Duser.timezone=Asia/Shanghai -jar pump30.jar
} catch {
    Write-Host "[错误] 启动程序失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "[信息] PUMP30价格监控系统已停止" -ForegroundColor Yellow
Read-Host "按回车键退出" 