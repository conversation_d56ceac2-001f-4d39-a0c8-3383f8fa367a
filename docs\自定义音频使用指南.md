# 🎵 PUMP监控系统自定义音频使用指南

## 📋 快速开始

### 1. **已生成的测试音频文件**
✅ 系统已自动生成测试音频文件：
- `sounds/buy_alert.wav` - 买入报警音（800Hz，2声）
- `sounds/sell_alert.wav` - 卖出报警音（1200Hz，3声）

### 2. **启用自定义音频**
✅ 配置已自动更新：
```properties
pump.alert.sound-type=CUSTOM
```

### 3. **立即测试**
重新启动PUMP监控系统，当差价超过$30时会播放自定义音频！

## 🎧 音频文件要求

### **技术规格**
- **格式**: WAV (必须)
- **编码**: PCM 16位
- **采样率**: 44.1kHz 或 22.05kHz
- **声道**: 单声道或立体声
- **文件大小**: 建议 < 1MB
- **播放时长**: 建议 1-3秒

### **文件命名**
- `buy_alert.wav` - 买入套利机会报警音
- `sell_alert.wav` - 卖出套利机会报警音

## 📂 音频文件放置位置

### **方式1: 项目资源目录（推荐）**
```
src/main/resources/sounds/
├── buy_alert.wav   # 买入报警音
└── sell_alert.wav  # 卖出报警音
```

### **方式2: 运行目录（当前使用）**
```
./sounds/
├── buy_alert.wav   # 买入报警音  
└── sell_alert.wav  # 卖出报警音
```

## 🔧 自定义音频制作

### **使用专业软件**
1. **Audacity** (免费)
   - 下载: https://www.audacityteam.org/
   - 导出格式: WAV (PCM 16位, 44.1kHz)

2. **Adobe Audition** (付费)
   - 专业音频编辑软件
   - 支持各种音频格式转换

### **在线工具**
1. **Online Audio Converter**
   - 网址: https://online-audio-converter.com/
   - 支持多种格式转WAV

2. **Convertio**
   - 网址: https://convertio.co/audio-converter/
   - 在线音频格式转换

### **音频建议**
- **买入音**: 较低频率 (500-1000Hz)，短促有力
- **卖出音**: 较高频率 (1000-1500Hz)，清脆明亮
- **音量**: 适中，避免过于刺耳
- **时长**: 1-2秒最佳，避免过长影响监控

## ⚙️ 配置选项

### **音频类型设置**
```properties
# 系统提示音 (默认)
pump.alert.sound-type=SYSTEM

# 多重提示音 (更响亮)
pump.alert.sound-type=MULTIPLE

# 自定义WAV文件
pump.alert.sound-type=CUSTOM
```

### **报警阈值设置**
```properties
# 买入报警阈值 (美元)
pump.alert.buy-threshold=30.00

# 卖出报警阈值 (美元)  
pump.alert.sell-threshold=30.00

# 冷却时间 (毫秒)
pump.alert.cooldown=30000
```

## 🚨 故障排除

### **音频不播放**
1. **检查文件存在**:
   ```bash
   ls -la sounds/
   # 应该看到 buy_alert.wav 和 sell_alert.wav
   ```

2. **检查文件格式**:
   - 确保是WAV格式
   - 确保不是损坏的文件

3. **检查配置**:
   ```properties
   pump.alert.sound-type=CUSTOM  # 确保设置正确
   ```

### **音频播放失败**
- 系统会自动回退到系统提示音
- 查看日志中的错误信息：
  ```
  播放自定义音频失败: xxx，使用系统提示音
  ```

### **音频质量问题**
- 确保采样率为44.1kHz或22.05kHz
- 确保是16位PCM编码
- 避免使用压缩格式（MP3、AAC等）

## 🎯 测试音频效果

### **手动触发测试**
1. 启动监控系统
2. 等待价格差异超过$30阈值
3. 听到自定义音频说明配置成功

### **调整音频文件**
1. 替换 `sounds/buy_alert.wav` 或 `sounds/sell_alert.wav`
2. 重新启动系统
3. 新音频会立即生效

## 💡 高级技巧

### **不同场景使用不同音频**
- **小额套利** (差价$30-50): 轻柔提示音
- **中等套利** (差价$50-100): 标准报警音  
- **大额套利** (差价$100+): 紧急报警音

### **音频个性化**
- 使用自己喜欢的音效
- 录制语音提示 ("买入机会", "卖出机会")
- 使用不同乐器音色区分买卖

## ✅ 配置完成检查清单

- [ ] 音频文件已放置在正确位置
- [ ] 文件格式为WAV (PCM 16位)
- [ ] 配置设置为 `pump.alert.sound-type=CUSTOM`
- [ ] 系统重新启动
- [ ] 测试音频播放正常

现在您的PUMP监控系统已支持完全自定义的报警音效！🎉
