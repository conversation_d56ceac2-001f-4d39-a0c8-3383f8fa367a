import java.math.BigDecimal;

/**
 * 测试修复版本的Jupiter API客户端
 * 
 * <AUTHOR> Agent
 * @version 1.0
 * @since 2025-01-15
 */
public class TestJupiterFixed {
    
    public static void main(String[] args) {
        System.out.println("=== 测试修复版本的Jupiter API客户端 ===");
        System.out.println();
        
        // 创建客户端实例（手动设置配置）
        TestJupiterApiClient client = new TestJupiterApiClient();
        
        // 测试1: 获取价格数据
        System.out.println("1. 测试获取价格数据...");
        TestPriceData priceData = client.getPumpPrice();
        if (priceData != null && priceData.isValid()) {
            System.out.println("✅ 价格获取成功: $" + priceData.getPrice());
        } else {
            System.out.println("❌ 价格获取失败: " + (priceData != null ? priceData.getErrorMessage() : "null"));
        }
        
        // 测试2: 健康检查
        System.out.println("\n2. 测试API健康检查...");
        boolean isHealthy = client.isApiHealthy();
        System.out.println(isHealthy ? "✅ API健康检查通过" : "❌ API健康检查失败");
        
        // 测试3: 获取报价
        System.out.println("\n3. 测试获取报价...");
        BigDecimal buyQuote = client.getQuotePrice(new BigDecimal("100"), true);
        if (buyQuote != null) {
            System.out.println("✅ 买入报价获取成功: " + buyQuote);
        } else {
            System.out.println("❌ 买入报价获取失败");
        }
        
        System.out.println("\n=== 测试完成 ===");
    }
    
    /**
     * 简化的Jupiter API客户端测试版本
     */
    static class TestJupiterApiClient {
        private final String baseUrl = "https://lite-api.jup.ag/price/v3";
        private final int timeout = 15000;
        private final boolean proxyEnabled = true;
        private final String proxyHost = "127.0.0.1";
        private final int proxyPort = 7890;
        private final String proxyType = "HTTP";
        
        private static final String PUMP_TOKEN = "83cXjb9Z8iJSBK2DBacte1utUsygdotMBQVhJoSppump";
        
        public TestPriceData getPumpPrice() {
            try {
                String url = String.format("%s?ids=%s", baseUrl, PUMP_TOKEN);
                java.net.HttpURLConnection connection = createConnection(url);
                
                int responseCode = connection.getResponseCode();
                if (responseCode == 200) {
                    String responseBody = readResponse(connection);
                    return parseV3Response(responseBody);
                } else {
                    return new TestPriceData("API请求失败，响应代码: " + responseCode);
                }
                
            } catch (Exception e) {
                return new TestPriceData("获取价格数据失败: " + e.getMessage());
            }
        }
        
        public boolean isApiHealthy() {
            try {
                String healthUrl = "https://lite-api.jup.ag/health";
                java.net.HttpURLConnection connection = createConnection(healthUrl);
                return connection.getResponseCode() == 200;
            } catch (Exception e) {
                return false;
            }
        }
        
        public BigDecimal getQuotePrice(BigDecimal amount, boolean isBuy) {
            try {
                String inputMint, outputMint;
                if (isBuy) {
                    inputMint = "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"; // USDT
                    outputMint = PUMP_TOKEN;
                } else {
                    inputMint = PUMP_TOKEN;
                    outputMint = "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"; // USDT
                }
                
                BigDecimal amountInSmallestUnit = amount.multiply(new BigDecimal("1000000"));
                String quoteUrl = baseUrl.replace("/price/v3", "/swap/v1/quote");
                String url = String.format("%s?inputMint=%s&outputMint=%s&amount=%s&slippageBps=50", 
                                         quoteUrl, inputMint, outputMint, amountInSmallestUnit.toBigInteger());
                
                java.net.HttpURLConnection connection = createConnection(url);
                int responseCode = connection.getResponseCode();
                
                if (responseCode == 200) {
                    String responseBody = readResponse(connection);
                    return parseQuoteResponse(responseBody);
                }
                
            } catch (Exception e) {
                System.out.println("报价获取异常: " + e.getMessage());
            }
            return null;
        }
        
        private java.net.HttpURLConnection createConnection(String urlString) throws Exception {
            java.net.URL url = new java.net.URL(urlString);
            java.net.HttpURLConnection connection;
            
            if (proxyEnabled) {
                java.net.Proxy.Type type = "SOCKS".equalsIgnoreCase(proxyType) ? 
                    java.net.Proxy.Type.SOCKS : java.net.Proxy.Type.HTTP;
                java.net.Proxy proxy = new java.net.Proxy(type, 
                    new java.net.InetSocketAddress(proxyHost, proxyPort));
                connection = (java.net.HttpURLConnection) url.openConnection(proxy);
            } else {
                connection = (java.net.HttpURLConnection) url.openConnection();
            }
            
            connection.setRequestMethod("GET");
            connection.setRequestProperty("User-Agent", "PUMP-Monitor/1.0");
            connection.setRequestProperty("Accept", "application/json");
            connection.setConnectTimeout(timeout);
            connection.setReadTimeout(timeout);
            
            return connection;
        }
        
        private String readResponse(java.net.HttpURLConnection connection) throws Exception {
            java.io.BufferedReader reader = new java.io.BufferedReader(
                new java.io.InputStreamReader(connection.getInputStream()));
            StringBuilder response = new StringBuilder();
            String line;
            
            while ((line = reader.readLine()) != null) {
                response.append(line);
            }
            reader.close();
            
            return response.toString();
        }
        
        private TestPriceData parseV3Response(String responseBody) {
            try {
                if (responseBody.contains(PUMP_TOKEN) && responseBody.contains("usdPrice")) {
                    String priceStr = extractPrice(responseBody);
                    if (priceStr != null) {
                        BigDecimal price = new BigDecimal(priceStr);
                        return new TestPriceData(price);
                    }
                }
                return new TestPriceData("API返回格式错误或无价格数据");
            } catch (Exception e) {
                return new TestPriceData("解析价格数据失败: " + e.getMessage());
            }
        }
        
        private BigDecimal parseQuoteResponse(String responseBody) {
            try {
                String searchKey = "\"outAmount\":\"";
                int startIndex = responseBody.indexOf(searchKey);
                if (startIndex == -1) return null;
                
                startIndex += searchKey.length();
                int endIndex = responseBody.indexOf("\"", startIndex);
                if (endIndex == -1) return null;
                
                String amountStr = responseBody.substring(startIndex, endIndex);
                BigDecimal outAmount = new BigDecimal(amountStr);
                return outAmount.divide(new BigDecimal("1000000"));
                
            } catch (Exception e) {
                return null;
            }
        }
        
        private String extractPrice(String json) {
            try {
                String searchKey = "\"usdPrice\":";
                int startIndex = json.indexOf(searchKey);
                if (startIndex == -1) return null;
                
                startIndex += searchKey.length();
                int endIndex = json.indexOf(",", startIndex);
                if (endIndex == -1) endIndex = json.indexOf("}", startIndex);
                if (endIndex == -1) return null;
                
                String priceStr = json.substring(startIndex, endIndex).trim();
                if (priceStr.startsWith("\"") && priceStr.endsWith("\"")) {
                    priceStr = priceStr.substring(1, priceStr.length() - 1);
                }
                
                return priceStr;
            } catch (Exception e) {
                return null;
            }
        }
    }
    
    /**
     * 简化的价格数据类
     */
    static class TestPriceData {
        private BigDecimal price;
        private String errorMessage;
        
        public TestPriceData(BigDecimal price) {
            this.price = price;
        }
        
        public TestPriceData(String errorMessage) {
            this.errorMessage = errorMessage;
        }
        
        public boolean isValid() {
            return price != null && price.compareTo(BigDecimal.ZERO) > 0;
        }
        
        public BigDecimal getPrice() {
            return price;
        }
        
        public String getErrorMessage() {
            return errorMessage;
        }
    }
}
