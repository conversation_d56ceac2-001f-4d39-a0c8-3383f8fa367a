# PUMP代币价格监控系统 - 实现逻辑详解

## 📋 目录
- [价格监控逻辑](#价格监控逻辑)
- [差价计算算法](#差价计算算法)
- [告警触发机制](#告警触发机制)
- [配置管理策略](#配置管理策略)
- [缓存优化机制](#缓存优化机制)
- [错误处理策略](#错误处理策略)
- [性能优化方案](#性能优化方案)

---

## 💰 价格监控逻辑

### 核心监控流程
系统采用定时调度器每2秒执行一次价格监控任务，获取100万PUMP代币在CEX和DEX的价格数据。

#### 1. 调度器执行逻辑
<augment_code_snippet path="src/main/java/com/pump/scheduler/PriceMonitorScheduler.java" mode="EXCERPT">
````java
@Scheduled(fixedDelay = 1000)
public void monitorPrices() {
    long currentTime = System.currentTimeMillis();
    if (currentTime - lastExecutionTime < configService.getMonitorInterval()) {
        return; // 动态间隔控制
    }
    lastExecutionTime = currentTime;
    taskCounter++;
    
    outputSimplifiedPrices(); // 执行价格监控
}
````
</augment_code_snippet>

**关键特性**:
- **动态间隔控制**: 基于配置文件的监控间隔 (默认2000ms)
- **任务计数器**: 跟踪执行次数，便于调试和监控
- **异常处理**: 捕获并记录所有监控异常

#### 2. Gate.io CEX价格获取
<augment_code_snippet path="src/main/java/com/pump/client/GateIoApiClient.java" mode="EXCERPT">
````java
public PriceData getPumpPrice() {
    String url = String.format("%s/spot/tickers?currency_pair=%s", baseUrl, SYMBOL);
    String response = restTemplate.getForObject(url, String.class);
    
    JsonNode jsonArray = objectMapper.readTree(response);
    if (jsonArray.isArray() && jsonArray.size() > 0) {
        JsonNode ticker = jsonArray.get(0);
        BigDecimal lastPrice = new BigDecimal(ticker.get("last").asText());
        // 返回单价，调用方计算总价
        return priceData;
    }
}
````
</augment_code_snippet>

**实现细节**:
- **API端点**: `/spot/tickers?currency_pair=PUMP_USDT`
- **数据解析**: 提取`last`字段作为最新价格
- **单价返回**: 返回单个PUMP的USDT价格
- **总价计算**: 在调度器中乘以1,000,000得到总价

#### 3. Jupiter DEX价格获取
系统使用两个Jupiter API客户端获取DEX价格：

##### JupiterApiClientFixed (主要客户端)
<augment_code_snippet path="src/main/java/com/pump/client/JupiterApiClientFixed.java" mode="EXCERPT">
````java
public BigDecimal getQuotePrice(BigDecimal amount, boolean isBuy) {
    if (isBuy) {
        // 买入逻辑：用USDT买PUMP
        inputMint = "USDT";
        outputMint = "PUMP";
        // 估算需要的USDT数量 = PUMP数量 × 基础价格 × 1.2倍余量
        inputAmount = amount.multiply(basePrice.getLastPrice()).multiply(new BigDecimal("1.2"));
    } else {
        // 卖出逻辑：卖PUMP换USDT
        inputMint = "PUMP";
        outputMint = "USDT";
        inputAmount = amount; // 直接使用PUMP数量
    }
}
````
</augment_code_snippet>

**买入价格计算逻辑**:
1. 获取PUMP基础价格 (通过Price API)
2. 估算所需USDT = PUMP数量 × 基础价格 × 1.2
3. 调用Quote API获取实际兑换比例
4. 计算单个PUMP的实际买入价格

**卖出价格计算逻辑**:
1. 直接使用PUMP数量调用Quote API
2. 获取可兑换的USDT数量
3. 计算单个PUMP的实际卖出价格

##### JupiterUltraApiClient (增强客户端)
<augment_code_snippet path="src/main/java/com/pump/client/JupiterUltraApiClient.java" mode="EXCERPT">
````java
public BigDecimal getUltraQuotePrice(BigDecimal amount, boolean isBuy) {
    String url = String.format("%s?inputMint=%s&outputMint=%s&amount=%s&taker=%s",
        ULTRA_BASE_URL, inputMint, outputMint, inputAmount.toBigInteger(), TAKER_ADDRESS);
    
    // 使用Ultra API的统一路由引擎
    BigDecimal unitPrice = parseUltraResponse(responseBody, amount, isBuy);
    return unitPrice;
}
````
</augment_code_snippet>

**Ultra API特性**:
- **统一路由**: 整合Metis v1和Jupiter Z引擎
- **更优价格**: 通过多路径路由获得更好的价格
- **固定Taker**: 使用预设地址获取可执行交易

---

## 📊 差价计算算法

### 价格差异计算公式

系统使用以下公式计算买入和卖出的价格差异：

#### 买入差价计算
```
买入差价 = Gate.io总价 - Jupiter买入总价
```

<augment_code_snippet path="src/main/java/com/pump/scheduler/PriceMonitorScheduler.java" mode="EXCERPT">
````java
if (jupiterBuyTotalPrice != null) {
    // 买入差价 = Gate.io总价 - Ultra API买入总价
    BigDecimal buyDifference = gateTotalPrice.subtract(jupiterBuyTotalPrice);
    logger.info("{} : 池买入100W个PUMP: ${}，差价：${}，做升",
        timestamp,
        String.format("%.2f", jupiterBuyTotalPrice),
        String.format("%.2f", buyDifference));
    
    alertSoundService.checkBuyAlert(buyDifference, jupiterBuyTotalPrice, gateTotalPrice);
}
````
</augment_code_snippet>

**业务含义**:
- **正差价**: Gate.io价格高于Jupiter，存在买入套利机会
- **负差价**: Jupiter价格高于Gate.io，不适合买入套利
- **交易建议**: "做升" - 在Jupiter买入，在Gate.io卖出

#### 卖出差价计算
```
卖出差价 = Jupiter卖出总价 - Gate.io总价
```

<augment_code_snippet path="src/main/java/com/pump/scheduler/PriceMonitorScheduler.java" mode="EXCERPT">
````java
if (jupiterSellTotalPrice != null) {
    // 卖出差价 = Ultra API卖出总价 - Gate.io总价
    BigDecimal sellDifference = jupiterSellTotalPrice.subtract(gateTotalPrice);
    logger.info("{} : 池卖出100W个PUMP: ${}，差价：${}，做跌",
        timestamp,
        String.format("%.2f", jupiterSellTotalPrice),
        String.format("%.2f", sellDifference));
    
    alertSoundService.checkSellAlert(sellDifference, jupiterSellTotalPrice, gateTotalPrice);
}
````
</augment_code_snippet>

**业务含义**:
- **正差价**: Jupiter卖出价高于Gate.io，存在卖出套利机会
- **负差价**: Gate.io价格高于Jupiter卖出价，不适合卖出套利
- **交易建议**: "做跌" - 在Gate.io买入，在Jupiter卖出

### 价格精度处理
- **计算精度**: 使用BigDecimal确保高精度计算
- **显示精度**: 格式化为2位小数 (`String.format("%.2f", price)`)
- **阈值比较**: 使用BigDecimal.compareTo()进行精确比较

---

## 🔔 告警触发机制

### 告警条件判断
系统基于价格差异阈值触发音频告警，默认阈值为$30。

#### 买入告警逻辑
<augment_code_snippet path="src/main/java/com/pump/service/AlertSoundService.java" mode="EXCERPT">
````java
public void checkBuyAlert(BigDecimal buyDifference, BigDecimal jupiterPrice, BigDecimal gatePrice) {
    BigDecimal buyThreshold = new BigDecimal(configService.getBuyThreshold());
    
    if (buyDifference.compareTo(buyThreshold) > 0 && !isBuyAudioPlaying) {
        String message = String.format("买入机会: Gate.io($%.2f) - Jupiter($%.2f) = $%.2f > $%.2f",
            gatePrice, jupiterPrice, buyDifference, buyThreshold);
        
        triggerAlert(AlertType.BUY_OPPORTUNITY, message);
    }
}
````
</augment_code_snippet>

**触发条件**:
1. 买入差价 > 配置的买入阈值 ($30)
2. 当前没有买入音频正在播放 (防重复)

#### 卖出告警逻辑
<augment_code_snippet path="src/main/java/com/pump/service/AlertSoundService.java" mode="EXCERPT">
````java
public void checkSellAlert(BigDecimal sellDifference, BigDecimal jupiterPrice, BigDecimal gatePrice) {
    BigDecimal sellThreshold = new BigDecimal(configService.getSellThreshold());
    
    if (sellDifference.compareTo(sellThreshold) > 0 && !isSellAudioPlaying) {
        String message = String.format("卖出机会: Jupiter($%.2f) - Gate.io($%.2f) = $%.2f > $%.2f",
            jupiterPrice, gatePrice, sellDifference, sellThreshold);
        
        triggerAlert(AlertType.SELL_OPPORTUNITY, message);
    }
}
````
</augment_code_snippet>

### 音频播放机制
<augment_code_snippet path="src/main/java/com/pump/service/AlertSoundService.java" mode="EXCERPT">
````java
private void triggerAlert(AlertType alertType, String message) {
    CompletableFuture.runAsync(() -> {
        try {
            // 设置播放状态
            if (alertType == AlertType.BUY_OPPORTUNITY) {
                isBuyAudioPlaying = true;
            } else {
                isSellAudioPlaying = true;
            }
            
            String soundType = configService.getSoundType();
            switch (soundType.toUpperCase()) {
                case "CUSTOM":
                    playCustomSound(alertType);
                    break;
                case "SYSTEM":
                    playSystemBeep(alertType);
                    break;
            }
        } finally {
            // 重置播放状态
            resetPlayingStatus(alertType);
        }
    });
}
````
</augment_code_snippet>

**音频文件映射**:
- **买入告警**: `up.wav` (表示价格上升机会)
- **卖出告警**: `down.wav` (表示价格下降机会)

**冷却机制**:
- **播放状态控制**: 通过`isBuyAudioPlaying`和`isSellAudioPlaying`标志
- **异步播放**: 使用CompletableFuture避免阻塞主线程
- **状态重置**: 音频播放完成后自动重置状态

---

## ⚙️ 配置管理策略

### JSON配置文件结构
<augment_code_snippet path="src/main/resources/pump-config.json" mode="EXCERPT">
````json
{
  "monitor": {
    "interval": 2000,
    "amount": 1000000,
    "comment": "监控间隔(毫秒)和监控数量(PUMP代币数量)"
  },
  "alert": {
    "enabled": true,
    "buyThreshold": 30.00,
    "sellThreshold": 30.00,
    "soundType": "CUSTOM",
    "continuousPlay": true
  },
  "api": {
    "timeout": 30000,
    "retryDelay": 2000
  },
  "proxy": {
    "enabled": false,
    "host": "127.0.0.1",
    "port": 7890,
    "type": "SOCKS"
  }
}
````
</augment_code_snippet>

### 配置加载机制
<augment_code_snippet path="src/main/java/com/pump/config/PumpConfigService.java" mode="EXCERPT">
````java
@PostConstruct
public void loadConfig() {
    try {
        // 只加载内部配置文件
        ClassPathResource resource = new ClassPathResource(CONFIG_FILE);
        if (resource.exists()) {
            try (InputStream inputStream = resource.getInputStream()) {
                config = objectMapper.readTree(inputStream);
                logger.info("加载内部配置文件: {}", CONFIG_FILE);
            }
        } else {
            // 使用默认配置
            config = createDefaultConfig();
            logger.info("使用默认配置 (未找到配置文件)");
        }
    } catch (Exception e) {
        logger.error("加载配置文件失败: {}", e.getMessage(), e);
        throw new RuntimeException("配置文件加载失败", e);
    }
}
````
</augment_code_snippet>

**配置特性**:
- **内嵌配置**: JSON文件打包到JAR内部，避免外部依赖
- **默认值支持**: 配置文件缺失时使用代码中的默认值
- **类型安全**: 通过专门的getter方法提供类型转换
- **注释支持**: JSON中包含comment字段说明配置用途

### 配置参数说明

| 配置项 | 默认值 | 说明 |
|--------|--------|------|
| monitor.interval | 2000 | 监控间隔(毫秒) |
| monitor.amount | 1000000 | 监控的PUMP代币数量 |
| alert.buyThreshold | 30.00 | 买入告警阈值(美元) |
| alert.sellThreshold | 30.00 | 卖出告警阈值(美元) |
| alert.soundType | CUSTOM | 音频类型(CUSTOM/SYSTEM/MULTIPLE) |
| api.timeout | 30000 | API超时时间(毫秒) |
| proxy.enabled | false | 是否启用代理 |

---

## 🚀 缓存优化机制

### 价格缓存实现
<augment_code_snippet path="src/main/java/com/pump/cache/PriceCache.java" mode="EXCERPT">
````java
public class PriceCache {
    private final Map<String, CacheEntry> cache = new ConcurrentHashMap<>();
    private final long defaultTtl;
    
    public PriceData get(String key) {
        CacheEntry entry = cache.get(key);
        if (entry != null && !entry.isExpired()) {
            return entry.getData();
        }
        return null; // 缓存未命中或已过期
    }
    
    public void put(String key, PriceData data) {
        cache.put(key, new CacheEntry(data, System.currentTimeMillis() + defaultTtl));
    }
}
````
</augment_code_snippet>

**缓存策略**:
- **TTL机制**: 默认30秒过期时间
- **线程安全**: 使用ConcurrentHashMap
- **自动清理**: 过期数据自动失效
- **命中统计**: 跟踪缓存命中率

### API调用优化
- **Jupiter Price API**: 缓存30秒，减少基础价格查询
- **Quote API**: 不缓存，确保报价实时性
- **Gate.io API**: 不缓存，确保CEX价格准确性

---

## 🛡️ 错误处理策略

### API调用重试机制
<augment_code_snippet path="src/main/java/com/pump/client/JupiterApiClientFixed.java" mode="EXCERPT">
````java
for (int attempt = 1; attempt <= MAX_RETRIES; attempt++) {
    try {
        HttpURLConnection connection = createConnection(url);
        int responseCode = connection.getResponseCode();
        
        // 处理429限流错误
        if (responseCode == 429) {
            logger.warn("Jupiter Quote API限流，等待后重试...");
            Thread.sleep(2000);
            continue;
        }
        
        if (responseCode == 200) {
            return parseResponse(connection);
        }
    } catch (Exception e) {
        if (attempt == MAX_RETRIES) {
            logger.error("API调用最终失败", e);
            return null;
        }
        // 指数退避
        Thread.sleep(BASE_RETRY_DELAY * (long)Math.pow(BACKOFF_MULTIPLIER, attempt - 1));
    }
}
````
</augment_code_snippet>

**重试策略**:
- **最大重试次数**: 3次
- **指数退避**: 1s → 2s → 4s
- **限流处理**: 429错误特殊处理
- **最大延迟**: 10秒上限

### 异常分类处理
1. **网络异常**: 重试机制 + 降级处理
2. **解析异常**: 记录详细日志 + 返回错误标记
3. **配置异常**: 启动时快速失败
4. **音频异常**: 静默处理，不影响主流程

---

## 🔧 编码处理机制

### UTF-8编码强制初始化
系统在启动时强制设置UTF-8编码，确保中文字符正确显示。

<augment_code_snippet path="src/main/java/com/pump/config/EncodingInitializer.java" mode="EXCERPT">
````java
public class EncodingInitializer {
    public static void forceInitializeUTF8() {
        try {
            // 强制设置系统属性
            System.setProperty("file.encoding", "UTF-8");
            System.setProperty("console.encoding", "UTF-8");
            System.setProperty("sun.jnu.encoding", "UTF-8");
            System.setProperty("user.timezone", "Asia/Shanghai");

            // 强制设置默认字符集
            Field charset = Charset.class.getDeclaredField("defaultCharset");
            charset.setAccessible(true);
            charset.set(null, StandardCharsets.UTF_8);

        } catch (Exception e) {
            System.err.println("UTF-8编码初始化失败: " + e.getMessage());
        }
    }
}
````
</augment_code_snippet>

**编码处理策略**:
- **系统属性设置**: 在JVM启动时设置编码相关属性
- **默认字符集强制**: 通过反射修改Charset默认值
- **时区统一**: 设置为Asia/Shanghai时区
- **控制台编码**: 确保控制台输出正确显示中文

### 启动脚本编码配置
<augment_code_snippet path="pump.bat" mode="EXCERPT">
````batch
@echo off
REM PUMP30 启动器 - 自动设置UTF-8编码
chcp 65001 >nul 2>&1
java -Dfile.encoding=UTF-8 -Dconsole.encoding=UTF-8 -Dsun.jnu.encoding=UTF-8 -Duser.timezone=Asia/Shanghai -jar pump30.jar %*
````
</augment_code_snippet>

---

## 🎵 音频系统详细实现

### 音频文件管理
系统支持多种音频告警方式，音频文件内嵌到JAR包中。

#### 音频文件结构
```
src/main/resources/
├── up.wav          # 买入机会告警音
├── down.wav        # 卖出机会告警音
└── pump-config.json # 配置文件
```

#### 自定义音频播放实现
<augment_code_snippet path="src/main/java/com/pump/service/AlertSoundService.java" mode="EXCERPT">
````java
private void playCustomSound(AlertType alertType) {
    try {
        String audioFile = (alertType == AlertType.BUY_OPPORTUNITY) ? "up.wav" : "down.wav";

        // 从classpath加载音频文件
        InputStream audioStream = getClass().getClassLoader().getResourceAsStream(audioFile);
        if (audioStream == null) {
            logger.error("音频文件不存在: {}", audioFile);
            return;
        }

        // 使用AudioInputStream播放
        AudioInputStream audioInputStream = AudioSystem.getAudioInputStream(audioStream);
        Clip clip = AudioSystem.getClip();
        clip.open(audioInputStream);

        // 播放音频
        clip.start();

        // 等待播放完成
        while (clip.isRunning()) {
            Thread.sleep(100);
        }

        // 清理资源
        clip.close();
        audioInputStream.close();

    } catch (Exception e) {
        logger.error("播放自定义音频失败: {}", e.getMessage());
    }
}
````
</augment_code_snippet>

### 音频播放状态管理
```mermaid
stateDiagram-v2
    [*] --> 空闲状态
    空闲状态 --> 检查告警条件
    检查告警条件 --> 空闲状态: 条件不满足
    检查告警条件 --> 播放音频: 条件满足且未播放
    检查告警条件 --> 空闲状态: 条件满足但正在播放
    播放音频 --> 设置播放标志
    设置播放标志 --> 异步播放
    异步播放 --> 播放完成
    播放完成 --> 重置标志
    重置标志 --> 空闲状态
```

### 多种音频模式支持
<augment_code_snippet path="src/main/java/com/pump/service/AlertSoundService.java" mode="EXCERPT">
````java
private void triggerAlert(AlertType alertType, String message) {
    CompletableFuture.runAsync(() -> {
        String soundType = configService.getSoundType();
        switch (soundType.toUpperCase()) {
            case "SYSTEM":
                playSystemBeep(alertType);
                break;
            case "CUSTOM":
                playCustomSound(alertType);
                break;
            case "MULTIPLE":
                playMultipleBeeps(alertType);
                break;
            default:
                playSystemBeep(alertType);
        }
    });
}

private void playSystemBeep(AlertType alertType) {
    try {
        Toolkit.getDefaultToolkit().beep();
        if (alertType == AlertType.BUY_OPPORTUNITY) {
            Thread.sleep(200);
            Toolkit.getDefaultToolkit().beep();
        }
    } catch (Exception e) {
        logger.error("系统蜂鸣器播放失败", e);
    }
}
````
</augment_code_snippet>

---

## 🔄 重试与容错机制

### 指数退避重试算法
<augment_code_snippet path="src/main/java/com/pump/client/JupiterApiClientFixed.java" mode="EXCERPT">
````java
private static final int MAX_RETRIES = 3;
private static final long BASE_RETRY_DELAY = 1000; // 基础延迟1秒
private static final double BACKOFF_MULTIPLIER = 2.0; // 退避倍数
private static final long MAX_RETRY_DELAY = 10000; // 最大延迟10秒

private BigDecimal executeWithRetry(Supplier<BigDecimal> apiCall) {
    for (int attempt = 1; attempt <= MAX_RETRIES; attempt++) {
        try {
            return apiCall.get();
        } catch (Exception e) {
            if (attempt == MAX_RETRIES) {
                logger.error("API调用最终失败，已重试{}次", MAX_RETRIES, e);
                return null;
            }

            // 计算退避延迟
            long delay = Math.min(
                (long)(BASE_RETRY_DELAY * Math.pow(BACKOFF_MULTIPLIER, attempt - 1)),
                MAX_RETRY_DELAY
            );

            logger.warn("API调用失败，{}ms后进行第{}次重试", delay, attempt + 1);

            try {
                Thread.sleep(delay);
            } catch (InterruptedException ie) {
                Thread.currentThread().interrupt();
                return null;
            }
        }
    }
    return null;
}
````
</augment_code_snippet>

### 限流处理策略
```mermaid
graph TD
    A[API请求] --> B{检查响应码}
    B -->|200| C[解析响应]
    B -->|429| D[限流处理]
    B -->|其他错误| E[重试逻辑]

    D --> F[等待2秒]
    F --> G[重新请求]
    G --> H{重试成功?}
    H -->|是| C
    H -->|否| I[记录错误]

    E --> J[计算退避延迟]
    J --> K[等待延迟]
    K --> L{达到最大重试?}
    L -->|否| A
    L -->|是| I

    C --> M[返回结果]
    I --> N[返回null]
```

### 容错降级机制
<augment_code_snippet path="src/main/java/com/pump/service/impl/PumpPriceServiceImpl.java" mode="EXCERPT">
````java
public BigDecimal getDexBuyPrice(BigDecimal amount) {
    try {
        // 优先使用Ultra API
        BigDecimal ultraPrice = jupiterUltraApiClient.getUltraQuotePrice(amount, true);
        if (ultraPrice != null) {
            return ultraPrice.multiply(amount);
        }

        // 降级到标准Jupiter API
        logger.warn("Ultra API不可用，降级到标准Jupiter API");
        BigDecimal standardPrice = jupiterApiClient.getQuotePrice(amount, true);
        if (standardPrice != null) {
            return standardPrice.multiply(amount);
        }

        // 最后降级到缓存价格
        logger.warn("所有Jupiter API不可用，尝试使用缓存价格");
        PriceData cachedPrice = priceCache.get("jupiter:pump:price");
        if (cachedPrice != null && cachedPrice.isValid()) {
            return cachedPrice.getLastPrice().multiply(amount);
        }

    } catch (Exception e) {
        logger.error("获取DEX买入价格失败", e);
    }

    return null; // 所有方式都失败
}
````
</augment_code_snippet>

---

## 📊 数据模型详解

### PriceData核心数据结构
<augment_code_snippet path="src/main/java/com/pump/model/PriceData.java" mode="EXCERPT">
````java
public class PriceData {
    /** 交易所类型 */
    private String exchange;

    /** 交易对 */
    private String symbol;

    /** 买入价格 */
    private BigDecimal buyPrice;

    /** 卖出价格 */
    private BigDecimal sellPrice;

    /** 最新价格 */
    private BigDecimal lastPrice;

    /** 价格更新时间 */
    private LocalDateTime timestamp;

    /** 交易量 */
    private BigDecimal volume;

    /** 是否有效 */
    private boolean valid;

    /** 错误信息 */
    private String errorMessage;
}
````
</augment_code_snippet>

### 数据验证机制
```java
public boolean isValid() {
    return valid &&
           lastPrice != null &&
           lastPrice.compareTo(BigDecimal.ZERO) > 0 &&
           timestamp != null &&
           (errorMessage == null || errorMessage.isEmpty());
}

public void validate() {
    if (lastPrice == null || lastPrice.compareTo(BigDecimal.ZERO) <= 0) {
        this.valid = false;
        this.errorMessage = "价格数据无效";
    }

    if (timestamp == null) {
        this.timestamp = LocalDateTime.now();
    }

    // 检查价格合理性 (PUMP价格应在0.001-1 USDT范围内)
    if (lastPrice != null) {
        BigDecimal minPrice = new BigDecimal("0.001");
        BigDecimal maxPrice = new BigDecimal("1.000");

        if (lastPrice.compareTo(minPrice) < 0 || lastPrice.compareTo(maxPrice) > 0) {
            logger.warn("价格超出合理范围: {}, 预期范围: {} - {}",
                lastPrice, minPrice, maxPrice);
        }
    }
}
```

### 缓存数据结构
<augment_code_snippet path="src/main/java/com/pump/cache/PriceCache.java" mode="EXCERPT">
````java
public class PriceCache {
    private static class CacheEntry {
        private final PriceData data;
        private final long expirationTime;

        public CacheEntry(PriceData data, long expirationTime) {
            this.data = data;
            this.expirationTime = expirationTime;
        }

        public boolean isExpired() {
            return System.currentTimeMillis() > expirationTime;
        }

        public PriceData getData() {
            return data;
        }
    }

    // 缓存统计
    public static class CacheStats {
        private long hits = 0;
        private long misses = 0;
        private long evictions = 0;

        public double getHitRate() {
            long total = hits + misses;
            return total == 0 ? 0.0 : (double) hits / total;
        }
    }
}
````
</augment_code_snippet>

---

## 🔍 日志与监控实现

### 结构化日志输出
<augment_code_snippet path="src/main/java/com/pump/scheduler/PriceMonitorScheduler.java" mode="EXCERPT">
````java
private void outputSimplifiedPrices() {
    String timestamp = LocalDateTime.now().format(TIME_FORMATTER);
    BigDecimal amount = new BigDecimal("1000000");

    // 获取价格数据
    PriceData cexPrice = pumpPriceService.getCexPrice();
    BigDecimal jupiterBuyTotalPrice = pumpPriceService.getDexBuyPrice(amount);
    BigDecimal jupiterSellTotalPrice = pumpPriceService.getDexSellPrice(amount);

    // 格式化输出
    if (cexPrice != null && cexPrice.isValid()) {
        BigDecimal gateTotalPrice = cexPrice.getLastPrice().multiply(amount);

        // 第一行：标题 + Gate价格
        logger.info("{} : ===============PUMP监控 ===============GATE单价: ${}",
            timestamp, String.format("%.2f", gateTotalPrice));

        // 第二行：买入价格和差价
        if (jupiterBuyTotalPrice != null) {
            BigDecimal buyDifference = gateTotalPrice.subtract(jupiterBuyTotalPrice);
            logger.info("{} : 池买入100W个PUMP: ${}，差价：${}，做升",
                timestamp,
                String.format("%.2f", jupiterBuyTotalPrice),
                String.format("%.2f", buyDifference));

            // 触发买入告警检查
            alertSoundService.checkBuyAlert(buyDifference, jupiterBuyTotalPrice, gateTotalPrice);
        }

        // 第三行：卖出价格和差价
        if (jupiterSellTotalPrice != null) {
            BigDecimal sellDifference = jupiterSellTotalPrice.subtract(gateTotalPrice);
            logger.info("{} : 池卖出100W个PUMP: ${}，差价：${}，做跌",
                timestamp,
                String.format("%.2f", jupiterSellTotalPrice),
                String.format("%.2f", sellDifference));

            // 触发卖出告警检查
            alertSoundService.checkSellAlert(sellDifference, jupiterSellTotalPrice, gateTotalPrice);
        }
    }
}
````
</augment_code_snippet>

### 性能监控指标
```java
public class PerformanceMonitor {
    private final AtomicLong apiCallCount = new AtomicLong(0);
    private final AtomicLong successCount = new AtomicLong(0);
    private final AtomicLong errorCount = new AtomicLong(0);
    private final AtomicLong totalResponseTime = new AtomicLong(0);

    public void recordApiCall(long responseTime, boolean success) {
        apiCallCount.incrementAndGet();
        totalResponseTime.addAndGet(responseTime);

        if (success) {
            successCount.incrementAndGet();
        } else {
            errorCount.incrementAndGet();
        }
    }

    public double getSuccessRate() {
        long total = apiCallCount.get();
        return total == 0 ? 0.0 : (double) successCount.get() / total;
    }

    public double getAverageResponseTime() {
        long count = apiCallCount.get();
        return count == 0 ? 0.0 : (double) totalResponseTime.get() / count;
    }
}
```

---

**文档版本**: v1.0
**最后更新**: 2025-01-16
**维护者**: AI Agent
