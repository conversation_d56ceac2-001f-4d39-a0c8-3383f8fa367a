import java.math.BigDecimal;

/**
 * 测试价格监控系统修复
 * 验证买入和卖出价格是否不同，以及监控是否持续运行
 * 
 * <AUTHOR> Agent
 * @version 1.0
 * @since 2025-01-15
 */
public class TestPriceMonitoringFix {
    
    public static void main(String[] args) {
        System.out.println("=== 测试价格监控系统修复 ===");
        System.out.println();
        
        // 模拟测试价格差异计算
        testPriceDifferenceCalculation();
        
        System.out.println("\n=== 测试完成 ===");
        System.out.println("请运行实际的Spring Boot应用程序来验证：");
        System.out.println("1. 买入和卖出价格是否不同");
        System.out.println("2. 监控是否每800ms持续运行");
        System.out.println("3. 调试日志是否显示详细的价格信息");
    }
    
    /**
     * 测试价格差异计算逻辑
     */
    private static void testPriceDifferenceCalculation() {
        System.out.println("1. 测试价格差异计算逻辑...");
        
        // 模拟价格数据
        BigDecimal basePrice = new BigDecimal("0.00587169");
        BigDecimal spread = basePrice.multiply(new BigDecimal("0.001")); // 0.1%价差
        
        BigDecimal buyPrice = basePrice.add(spread);
        BigDecimal sellPrice = basePrice.subtract(spread);
        
        System.out.println("基础价格: " + basePrice + " USDT/PUMP");
        System.out.println("买入价格: " + buyPrice + " USDT/PUMP");
        System.out.println("卖出价格: " + sellPrice + " USDT/PUMP");
        System.out.println("价差: " + spread + " USDT/PUMP");
        
        // 计算100万个PUMP的成本
        BigDecimal amount = new BigDecimal("1000000");
        BigDecimal buyTotalCost = buyPrice.multiply(amount);
        BigDecimal sellTotalValue = sellPrice.multiply(amount);
        
        System.out.println();
        System.out.println("100万个PUMP:");
        System.out.println("买入总成本: " + buyTotalCost + " USDT");
        System.out.println("卖出总收入: " + sellTotalValue + " USDT");
        System.out.println("价格差异: " + buyTotalCost.subtract(sellTotalValue) + " USDT");
        
        // 验证价格不同
        if (buyPrice.equals(sellPrice)) {
            System.out.println("❌ 错误：买入和卖出价格相同！");
        } else {
            System.out.println("✅ 正确：买入和卖出价格不同");
        }
    }
}
