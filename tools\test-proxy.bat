@echo off
chcp 65001 >nul
echo ========================================
echo 测试代理配置效果
echo ========================================

echo 设置Java环境...
set PATH=F:\java-1.8.0\bin;%PATH%
set JAVA_HOME=F:\java-1.8.0

echo 验证Java环境...
java -version

echo.
echo 运行应用程序 (代理端口: 7890)...
echo 按 Ctrl+C 停止监控
echo.

F:\java-1.8.0\bin\java.exe -cp "C:\Users\<USER>\AppData\Local\Temp\cp_4r1kuslcko47sr5lmwpnskhs1.jar" com.pump.PumpApplication

pause 