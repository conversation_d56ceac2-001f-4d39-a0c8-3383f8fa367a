import java.math.BigDecimal;

/**
 * 测试修复后的买入卖出逻辑
 * 验证修复是否正确
 * 
 * <AUTHOR> Agent
 * @version 1.0
 * @since 2025-01-15
 */
public class TestFixedLogic {
    
    public static void main(String[] args) {
        System.out.println("=== 测试修复后的买入卖出逻辑 ===");
        System.out.println();
        
        BigDecimal amount = new BigDecimal("1000000"); // 100万个PUMP
        
        // 模拟修复后的逻辑
        System.out.println("1. 调用 getBuyPrice(amount, \"DEX\"):");
        System.out.println("   -> 调用 getQuotePrice(amount, true)");
        System.out.println("   -> isBuy = true");
        simulateFixedGetQuotePrice(amount, true);
        
        System.out.println();
        
        System.out.println("2. 调用 getSellPrice(amount, \"DEX\"):");
        System.out.println("   -> 调用 getQuotePrice(amount, false)");
        System.out.println("   -> isBuy = false");
        simulateFixedGetQuotePrice(amount, false);
        
        System.out.println();
        System.out.println("=== 预期的API调用 ===");
        System.out.println("买入价格查询: inputMint=PUMP, outputMint=USDT, amount=1000000000000");
        System.out.println("  -> 查询卖出100万个PUMP能得到多少USDT");
        System.out.println("卖出价格查询: inputMint=USDT, outputMint=PUMP, amount=6000000000");
        System.out.println("  -> 查询用$6000能买到多少PUMP");
        System.out.println();
        System.out.println("这样应该与截图匹配！");
    }
    
    private static void simulateFixedGetQuotePrice(BigDecimal amount, boolean isBuy) {
        String inputMint, outputMint;
        BigDecimal queryAmount;
        
        if (isBuy) {
            // 买入PUMP：查询卖出价格作为参考
            inputMint = "pumpCmXqMfrsAkQ5r49WcJnRayYRqmXz6ae8H7H9Dfn"; // PUMP
            outputMint = "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB"; // USDT
            queryAmount = amount; // 使用100万个PUMP
            System.out.println("   买入逻辑（查询卖出价格）:");
            System.out.println("     inputMint: " + inputMint + " (PUMP)");
            System.out.println("     outputMint: " + outputMint + " (USDT)");
            System.out.println("     amount: " + queryAmount.multiply(new BigDecimal("1000000")) + " (100万PUMP最小单位)");
        } else {
            // 卖出PUMP：查询买入价格作为参考
            inputMint = "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB"; // USDT
            outputMint = "pumpCmXqMfrsAkQ5r49WcJnRayYRqmXz6ae8H7H9Dfn"; // PUMP
            queryAmount = new BigDecimal("6000"); // 固定使用$6000
            System.out.println("   卖出逻辑（查询买入价格）:");
            System.out.println("     inputMint: " + inputMint + " (USDT)");
            System.out.println("     outputMint: " + outputMint + " (PUMP)");
            System.out.println("     amount: " + queryAmount.multiply(new BigDecimal("1000000")) + " (6000 USDT最小单位)");
        }
    }
}
