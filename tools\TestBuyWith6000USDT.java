import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.Proxy;
import java.net.InetSocketAddress;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 测试用$6000买入PUMP的价格计算
 * 验证新的买入价格计算逻辑
 * 
 * <AUTHOR> Agent
 * @version 1.0
 * @since 2025-01-15
 */
public class TestBuyWith6000USDT {
    
    // 正确的代币地址
    private static final String PUMP_TOKEN = "pumpCmXqMfrsAkQ5r49WcJnRayYRqmXz6ae8H7H9Dfn";
    private static final String USDT_TOKEN = "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB";
    
    public static void main(String[] args) {
        System.out.println("=== 测试用$6000买入PUMP的价格计算 ===");
        System.out.println();
        
        // 测试买入：用$6000买PUMP
        testBuyWith6000USDT();
        
        System.out.println();
        
        // 测试卖出：卖100万个PUMP
        testSell1MillionPUMP();
        
        System.out.println("\n=== 测试完成 ===");
    }
    
    /**
     * 测试用$6000买入PUMP
     */
    private static void testBuyWith6000USDT() {
        try {
            System.out.println("1. 测试买入：用$6000买PUMP");
            
            // 买入参数：inputMint=USDT, outputMint=PUMP, amount=6000 USDT
            String inputMint = USDT_TOKEN;
            String outputMint = PUMP_TOKEN;
            BigDecimal usdtAmount = new BigDecimal("6000");
            BigDecimal amountInSmallestUnit = usdtAmount.multiply(new BigDecimal("1000000")); // 6位精度
            
            String url = String.format("https://lite-api.jup.ag/swap/v1/quote?inputMint=%s&outputMint=%s&amount=%s&slippageBps=50&swapMode=ExactIn",
                                     inputMint, outputMint, amountInSmallestUnit.toBigInteger());
            
            System.out.println("买入请求URL: " + url);
            System.out.println("参数:");
            System.out.println("  - inputMint: " + inputMint + " (USDT)");
            System.out.println("  - outputMint: " + outputMint + " (PUMP)");
            System.out.println("  - amount: " + amountInSmallestUnit.toBigInteger() + " ($6000的最小单位)");
            System.out.println("  - swapMode: ExactIn");
            System.out.println();
            
            String responseBody = sendRequest(url);
            if (responseBody != null) {
                parseBuyResponse(responseBody);
            }
            
        } catch (Exception e) {
            System.out.println("❌ 买入测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试卖出100万个PUMP
     */
    private static void testSell1MillionPUMP() {
        try {
            System.out.println("2. 测试卖出：卖100万个PUMP");
            
            // 卖出参数：inputMint=PUMP, outputMint=USDT, amount=1000000 PUMP
            String inputMint = PUMP_TOKEN;
            String outputMint = USDT_TOKEN;
            BigDecimal pumpAmount = new BigDecimal("1000000");
            BigDecimal amountInSmallestUnit = pumpAmount.multiply(new BigDecimal("1000000")); // 6位精度
            
            String url = String.format("https://lite-api.jup.ag/swap/v1/quote?inputMint=%s&outputMint=%s&amount=%s&slippageBps=50&swapMode=ExactIn",
                                     inputMint, outputMint, amountInSmallestUnit.toBigInteger());
            
            System.out.println("卖出请求URL: " + url);
            System.out.println("参数:");
            System.out.println("  - inputMint: " + inputMint + " (PUMP)");
            System.out.println("  - outputMint: " + outputMint + " (USDT)");
            System.out.println("  - amount: " + amountInSmallestUnit.toBigInteger() + " (100万个PUMP的最小单位)");
            System.out.println("  - swapMode: ExactIn");
            System.out.println();
            
            String responseBody = sendRequest(url);
            if (responseBody != null) {
                parseSellResponse(responseBody);
            }
            
        } catch (Exception e) {
            System.out.println("❌ 卖出测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 发送HTTP请求
     */
    private static String sendRequest(String urlString) {
        try {
            URL url = new URL(urlString);
            Proxy proxy = new Proxy(Proxy.Type.SOCKS, new InetSocketAddress("127.0.0.1", 7890));
            HttpURLConnection connection = (HttpURLConnection) url.openConnection(proxy);
            
            connection.setRequestMethod("GET");
            connection.setRequestProperty("User-Agent", "PUMP-Monitor/1.0");
            connection.setRequestProperty("Accept", "application/json");
            connection.setConnectTimeout(15000);
            connection.setReadTimeout(20000);
            
            int responseCode = connection.getResponseCode();
            System.out.println("响应代码: " + responseCode);
            
            if (responseCode == 200) {
                BufferedReader reader = new BufferedReader(
                    new InputStreamReader(connection.getInputStream()));
                StringBuilder response = new StringBuilder();
                String line;
                
                while ((line = reader.readLine()) != null) {
                    response.append(line);
                }
                reader.close();
                
                return response.toString();
            } else {
                System.out.println("❌ 请求失败，响应代码: " + responseCode);
            }
            
        } catch (Exception e) {
            System.out.println("❌ 请求异常: " + e.getMessage());
        }
        return null;
    }
    
    /**
     * 解析买入响应
     */
    private static void parseBuyResponse(String responseBody) {
        try {
            System.out.println("买入响应解析:");
            
            // 简单JSON解析
            String inAmountKey = "\"inAmount\":\"";
            String outAmountKey = "\"outAmount\":\"";
            
            int inStart = responseBody.indexOf(inAmountKey) + inAmountKey.length();
            int inEnd = responseBody.indexOf("\"", inStart);
            String inAmountStr = responseBody.substring(inStart, inEnd);
            
            int outStart = responseBody.indexOf(outAmountKey) + outAmountKey.length();
            int outEnd = responseBody.indexOf("\"", outStart);
            String outAmountStr = responseBody.substring(outStart, outEnd);
            
            BigDecimal inAmount = new BigDecimal(inAmountStr);  // USDT最小单位
            BigDecimal outAmount = new BigDecimal(outAmountStr); // PUMP最小单位
            
            // 转换为正常单位
            BigDecimal usdtNormal = inAmount.divide(new BigDecimal("1000000"), 8, RoundingMode.HALF_UP);
            BigDecimal pumpNormal = outAmount.divide(new BigDecimal("1000000"), 8, RoundingMode.HALF_UP);
            
            // 买入单价 = 输入USDT / 输出PUMP
            BigDecimal buyUnitPrice = usdtNormal.divide(pumpNormal, 8, RoundingMode.HALF_UP);
            
            System.out.println("  输入: " + usdtNormal + " USDT");
            System.out.println("  输出: " + pumpNormal + " PUMP");
            System.out.println("  买入单价: " + buyUnitPrice + " USDT/PUMP");
            
            // 计算100万个PUMP的买入总价
            BigDecimal millionPumpBuyPrice = buyUnitPrice.multiply(new BigDecimal("1000000"));
            System.out.println("  100万个PUMP买入总价: " + String.format("%.2f", millionPumpBuyPrice) + " USDT");
            
            // 计算用$6000能买到多少个PUMP
            System.out.println("  用$6000能买到: " + String.format("%.0f", pumpNormal) + " 个PUMP");
            
        } catch (Exception e) {
            System.out.println("❌ 买入响应解析失败: " + e.getMessage());
        }
    }
    
    /**
     * 解析卖出响应
     */
    private static void parseSellResponse(String responseBody) {
        try {
            System.out.println("卖出响应解析:");
            
            // 简单JSON解析
            String inAmountKey = "\"inAmount\":\"";
            String outAmountKey = "\"outAmount\":\"";
            
            int inStart = responseBody.indexOf(inAmountKey) + inAmountKey.length();
            int inEnd = responseBody.indexOf("\"", inStart);
            String inAmountStr = responseBody.substring(inStart, inEnd);
            
            int outStart = responseBody.indexOf(outAmountKey) + outAmountKey.length();
            int outEnd = responseBody.indexOf("\"", outStart);
            String outAmountStr = responseBody.substring(outStart, outEnd);
            
            BigDecimal inAmount = new BigDecimal(inAmountStr);  // PUMP最小单位
            BigDecimal outAmount = new BigDecimal(outAmountStr); // USDT最小单位
            
            // 转换为正常单位
            BigDecimal pumpNormal = inAmount.divide(new BigDecimal("1000000"), 8, RoundingMode.HALF_UP);
            BigDecimal usdtNormal = outAmount.divide(new BigDecimal("1000000"), 8, RoundingMode.HALF_UP);
            
            // 卖出单价 = 输出USDT / 输入PUMP
            BigDecimal sellUnitPrice = usdtNormal.divide(pumpNormal, 8, RoundingMode.HALF_UP);
            
            System.out.println("  输入: " + pumpNormal + " PUMP");
            System.out.println("  输出: " + usdtNormal + " USDT");
            System.out.println("  卖出单价: " + sellUnitPrice + " USDT/PUMP");
            System.out.println("  100万个PUMP卖出总价: " + String.format("%.2f", usdtNormal) + " USDT");
            
        } catch (Exception e) {
            System.out.println("❌ 卖出响应解析失败: " + e.getMessage());
        }
    }
}
