import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 测试新的输出格式
 * 验证价格监控系统的新显示格式
 * 
 * <AUTHOR> Agent
 * @version 1.0
 * @since 2025-01-15
 */
public class TestNewOutputFormat {
    
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
    
    public static void main(String[] args) {
        System.out.println("=== 新输出格式测试 ===");
        System.out.println();
        
        // 模拟价格数据
        BigDecimal gateUnitPrice = new BigDecimal("0.005634");
        BigDecimal gateTotalPrice = gateUnitPrice.multiply(new BigDecimal("1000000"));
        BigDecimal jupiterBuyTotalPrice = new BigDecimal("5641.95");
        BigDecimal jupiterSellTotalPrice = new BigDecimal("5635.63");
        
        // 计算差价
        BigDecimal buyDifference = jupiterBuyTotalPrice.subtract(gateTotalPrice);
        BigDecimal sellDifference = jupiterSellTotalPrice.subtract(gateTotalPrice);
        
        String timestamp = LocalDateTime.now().format(TIME_FORMATTER);
        
        System.out.println("=== 新格式输出示例 ===");
        System.out.println();
        
        // 第一行：分隔线标题 + GATE单价
        System.out.println(String.format("%s : ===============PUMP监控 ===============GATE单价: $%.2f",
            timestamp, gateUnitPrice));
        
        // 第二行：池买入价格 + 差价 + 做升
        System.out.println(String.format("%s : 池买入100W个PUMP: $%.2f，差价：$%.2f，做升",
            timestamp, 
            jupiterBuyTotalPrice,
            buyDifference));
        
        // 第三行：池卖出价格 + 差价 + 做跌
        System.out.println(String.format("%s : 池卖出100W个PUMP: $%.2f，差价：$%.2f，做跌",
            timestamp,
            jupiterSellTotalPrice,
            sellDifference));
        
        System.out.println();
        System.out.println("=== 格式说明 ===");
        System.out.println("✅ 时间戳格式: yyyy-MM-dd HH:mm:ss.SSS");
        System.out.println("✅ 分隔线标题: ===============PUMP监控 ===============");
        System.out.println("✅ 价格格式: $xxxx.xx (保留2位小数)");
        System.out.println("✅ 差价计算: DEX价格 - CEX价格");
        System.out.println("✅ 交易建议: 买入显示'做升'，卖出显示'做跌'");
        System.out.println("✅ 监控间隔: 1000ms (1秒)");
        
        System.out.println();
        System.out.println("=== 价格分析 ===");
        System.out.printf("Gate.io单价: $%.6f%n", gateUnitPrice);
        System.out.printf("Gate.io总价(100万个): $%.2f%n", gateTotalPrice);
        System.out.printf("Ultra API买入总价: $%.2f%n", jupiterBuyTotalPrice);
        System.out.printf("Ultra API卖出总价: $%.2f%n", jupiterSellTotalPrice);
        System.out.printf("买入差价: $%.2f (%s)%n", buyDifference, buyDifference.compareTo(BigDecimal.ZERO) > 0 ? "做升有利" : "做升不利");
        System.out.printf("卖出差价: $%.2f (%s)%n", sellDifference, sellDifference.compareTo(BigDecimal.ZERO) > 0 ? "做跌有利" : "做跌不利");
        
        System.out.println();
        System.out.println("现在请重新启动系统查看新的输出格式！");
    }
}
