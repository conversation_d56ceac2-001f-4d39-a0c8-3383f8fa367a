# 需要以管理员身份运行的Maven配置脚本
Write-Host "正在配置Maven环境变量..." -ForegroundColor Yellow

# 设置MAVEN_HOME环境变量
try {
    [Environment]::SetEnvironmentVariable("MAVEN_HOME", "F:\apache-maven-3.9.10", "Machine")
    Write-Host "✅ MAVEN_HOME环境变量设置成功" -ForegroundColor Green
} catch {
    Write-Host "❌ 设置MAVEN_HOME失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 获取当前PATH变量
$currentPath = [Environment]::GetEnvironmentVariable("Path", "Machine")

# 检查是否已经包含Maven路径
if ($currentPath -notlike "*%MAVEN_HOME%\bin*") {
    try {
        # 添加Maven bin目录到PATH
        $newPath = $currentPath + ";%MAVEN_HOME%\bin"
        [Environment]::SetEnvironmentVariable("Path", $newPath, "Machine")
        Write-Host "✅ PATH环境变量更新成功" -ForegroundColor Green
    } catch {
        Write-Host "❌ 更新PATH失败: $($_.Exception.Message)" -ForegroundColor Red
        exit 1
    }
} else {
    Write-Host "⚠️  PATH中已包含Maven路径，无需重复添加" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Maven环境变量配置完成！" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "请关闭当前PowerShell窗口并重新打开，然后运行：" -ForegroundColor Yellow
Write-Host "mvn -version" -ForegroundColor White
Write-Host ""
Write-Host "如果显示Maven版本信息，说明配置成功！" -ForegroundColor Green 