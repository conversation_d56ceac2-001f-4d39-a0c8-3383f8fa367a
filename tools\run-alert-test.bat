@echo off
chcp 65001 >nul
echo ===============================================
echo           PUMP Alert Sound Test Tool
echo ===============================================
echo.

REM Check Java environment
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Java not found, please install Java 8+
    pause
    exit /b 1
)

echo Java environment check passed
echo.

REM Compile test file
echo Compiling test file...
javac TestAlertSound.java
if %errorlevel% neq 0 (
    echo Compilation failed, please check code
    pause
    exit /b 1
)

echo Compilation successful
echo.

REM Check audio files
echo Checking audio files...
if exist "sounds\up.wav" (
    echo [OK] Found buy alert audio: sounds\up.wav
) else (
    echo [WARN] Buy alert audio not found: sounds\up.wav
)

if exist "sounds\down.wav" (
    echo [OK] Found sell alert audio: sounds\down.wav
) else (
    echo [WARN] Sell alert audio not found: sounds\down.wav
)

echo.
echo Starting test tool...
echo.

REM Run test
java TestAlertSound

echo.
echo Test completed
pause
