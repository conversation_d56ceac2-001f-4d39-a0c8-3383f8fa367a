import java.math.BigDecimal;
import java.awt.Toolkit;
import java.io.File;
import java.io.InputStream;
import javax.sound.sampled.*;

/**
 * Simple Alert Sound Test Tool
 * Test buy and sell alert sounds
 */
public class SimpleAlertTest {
    
    public static void main(String[] args) {
        System.out.println("=== PUMP Alert Sound Test ===");
        System.out.println();
        
        SimpleAlertTest test = new SimpleAlertTest();
        
        // Test configuration
        TestConfig config = new TestConfig();
        config.alertEnabled = true;
        config.buyThreshold = new BigDecimal("20.00");
        config.sellThreshold = new BigDecimal("20.00");
        config.soundType = "CUSTOM";
        
        System.out.println("Current Config:");
        System.out.println("Alert Enabled: " + config.alertEnabled);
        System.out.println("Buy Threshold: $" + config.buyThreshold);
        System.out.println("Sell Threshold: $" + config.sellThreshold);
        System.out.println("Sound Type: " + config.soundType);
        System.out.println();
        
        // Test 1: Buy Alert
        System.out.println("=== Test 1: Buy Alert ===");
        BigDecimal gatePrice = new BigDecimal("5620.00");
        BigDecimal ultraBuyPrice = new BigDecimal("5595.00");
        BigDecimal buyDifference = gatePrice.subtract(ultraBuyPrice); // $25
        
        System.out.println("Gate.io Price: $" + gatePrice);
        System.out.println("Ultra Buy Price: $" + ultraBuyPrice);
        System.out.println("Buy Difference: $" + buyDifference);
        
        if (buyDifference.compareTo(config.buyThreshold) > 0) {
            System.out.println("BUY ALERT TRIGGERED!");
            test.playBuyAlert(config);
        } else {
            System.out.println("Buy difference below threshold, no alert");
        }
        
        System.out.println();
        
        // Wait between tests
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // Test 2: Sell Alert
        System.out.println("=== Test 2: Sell Alert ===");
        BigDecimal ultraSellPrice = new BigDecimal("5630.00");
        BigDecimal gatePriceSell = new BigDecimal("5605.00");
        BigDecimal sellDifference = ultraSellPrice.subtract(gatePriceSell); // $25
        
        System.out.println("Ultra Sell Price: $" + ultraSellPrice);
        System.out.println("Gate.io Price: $" + gatePriceSell);
        System.out.println("Sell Difference: $" + sellDifference);
        
        if (sellDifference.compareTo(config.sellThreshold) > 0) {
            System.out.println("SELL ALERT TRIGGERED!");
            test.playSellAlert(config);
        } else {
            System.out.println("Sell difference below threshold, no alert");
        }
        
        System.out.println();
        
        // Test 3: System Sounds
        System.out.println("=== Test 3: System Sounds ===");
        config.soundType = "SYSTEM";
        System.out.println("Testing system buy sound (2 beeps)...");
        test.playSystemSound(2);
        
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        System.out.println("Testing system sell sound (3 beeps)...");
        test.playSystemSound(3);
        
        System.out.println();
        System.out.println("=== Test Completed ===");
    }
    
    private void playBuyAlert(TestConfig config) {
        if ("CUSTOM".equals(config.soundType)) {
            playCustomSound("up.wav");
        } else {
            playSystemSound(2);
        }
    }
    
    private void playSellAlert(TestConfig config) {
        if ("CUSTOM".equals(config.soundType)) {
            playCustomSound("down.wav");
        } else {
            playSystemSound(3);
        }
    }
    
    private void playCustomSound(String filename) {
        try {
            // Try to load from sounds directory
            File audioFile = new File("sounds/" + filename);
            if (audioFile.exists()) {
                System.out.println("Playing custom audio: " + audioFile.getAbsolutePath());
                playWavFile(audioFile);
                System.out.println("Custom audio playback completed");
            } else {
                // Try to load from resources
                InputStream audioStream = getClass().getClassLoader().getResourceAsStream("sounds/" + filename);
                if (audioStream != null) {
                    System.out.println("Playing resource audio: sounds/" + filename);
                    playWavFile(audioStream);
                    System.out.println("Resource audio playback completed");
                } else {
                    System.out.println("Custom audio file not found: " + filename + ", using system sound");
                    playSystemSound(filename.equals("up.wav") ? 2 : 3);
                }
            }
        } catch (Exception e) {
            System.out.println("Failed to play custom audio: " + e.getMessage() + ", using system sound");
            playSystemSound(filename.equals("up.wav") ? 2 : 3);
        }
    }
    
    private void playSystemSound(int beepCount) {
        try {
            Toolkit toolkit = Toolkit.getDefaultToolkit();
            System.out.println("Playing system sound (" + beepCount + " beeps)...");
            
            for (int i = 0; i < beepCount; i++) {
                toolkit.beep();
                if (i < beepCount - 1) {
                    Thread.sleep(200);
                }
            }
            
            System.out.println("System sound playback completed");
        } catch (Exception e) {
            System.out.println("Failed to play system sound: " + e.getMessage());
        }
    }
    
    private void playWavFile(File audioFile) throws Exception {
        try (AudioInputStream audioInputStream = AudioSystem.getAudioInputStream(audioFile)) {
            Clip clip = AudioSystem.getClip();
            clip.open(audioInputStream);
            clip.start();
            
            // Wait for playback to complete
            Thread.sleep(clip.getMicrosecondLength() / 1000);
            clip.close();
        }
    }
    
    private void playWavFile(InputStream audioStream) throws Exception {
        try (AudioInputStream audioInputStream = AudioSystem.getAudioInputStream(audioStream)) {
            Clip clip = AudioSystem.getClip();
            clip.open(audioInputStream);
            clip.start();
            
            // Wait for playback to complete
            Thread.sleep(clip.getMicrosecondLength() / 1000);
            clip.close();
        }
    }
    
    static class TestConfig {
        boolean alertEnabled;
        BigDecimal buyThreshold;
        BigDecimal sellThreshold;
        String soundType;
    }
}
