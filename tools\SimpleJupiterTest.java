import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.math.BigDecimal;

/**
 * 简单的Jupiter API测试工具
 * 不依赖Spring框架，直接测试API连接
 * 
 * <AUTHOR> Agent
 * @version 1.0
 * @since 2025-01-15
 */
public class SimpleJupiterTest {
    
    // PUMP代币地址
    private static final String PUMP_TOKEN = "83cXjb9Z8iJSBK2DBacte1utUsygdotMBQVhJoSppump";
    
    // Jupiter API V3 URL
    private static final String API_URL = "https://lite-api.jup.ag/price/v3?ids=" + PUMP_TOKEN;
    
    public static void main(String[] args) {
        System.out.println("=== Jupiter API V3 连接测试 ===");
        System.out.println("测试URL: " + API_URL);
        System.out.println("代币地址: " + PUMP_TOKEN);
        System.out.println();
        
        try {
            // 创建HTTP连接
            URL url = new URL(API_URL);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            
            // 设置请求属性
            connection.setRequestMethod("GET");
            connection.setRequestProperty("User-Agent", "PUMP-Monitor/1.0");
            connection.setRequestProperty("Accept", "application/json");
            connection.setConnectTimeout(15000);
            connection.setReadTimeout(20000);
            
            // 发送请求
            System.out.println("正在连接Jupiter API...");
            int responseCode = connection.getResponseCode();
            System.out.println("响应代码: " + responseCode);
            
            if (responseCode == 200) {
                // 读取响应
                BufferedReader reader = new BufferedReader(
                    new InputStreamReader(connection.getInputStream()));
                StringBuilder response = new StringBuilder();
                String line;
                
                while ((line = reader.readLine()) != null) {
                    response.append(line);
                }
                reader.close();
                
                String responseBody = response.toString();
                System.out.println("响应内容: " + responseBody);
                
                // 简单解析价格数据
                if (responseBody.contains(PUMP_TOKEN) && responseBody.contains("usdPrice")) {
                    // 提取价格信息
                    String priceStr = extractPrice(responseBody);
                    if (priceStr != null) {
                        BigDecimal price = new BigDecimal(priceStr);
                        System.out.println();
                        System.out.println("=== 价格数据解析成功 ===");
                        System.out.println("PUMP价格: $" + price);
                        System.out.println("API版本: V3");
                        System.out.println("数据来源: Jupiter");
                        System.out.println("测试结果: ✅ 成功");
                    } else {
                        System.out.println("❌ 价格数据解析失败");
                    }
                } else {
                    System.out.println("❌ 响应格式不正确或缺少价格数据");
                }
                
            } else {
                System.out.println("❌ API请求失败，响应代码: " + responseCode);
                
                // 读取错误信息
                BufferedReader errorReader = new BufferedReader(
                    new InputStreamReader(connection.getErrorStream()));
                StringBuilder errorResponse = new StringBuilder();
                String errorLine;
                
                while ((errorLine = errorReader.readLine()) != null) {
                    errorResponse.append(errorLine);
                }
                errorReader.close();
                
                System.out.println("错误信息: " + errorResponse.toString());
            }
            
        } catch (Exception e) {
            System.out.println("❌ 连接失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println();
        System.out.println("=== 测试完成 ===");
    }
    
    /**
     * 从JSON响应中提取价格
     */
    private static String extractPrice(String json) {
        try {
            // 简单的字符串解析，查找usdPrice字段
            String searchKey = "\"usdPrice\":";
            int startIndex = json.indexOf(searchKey);
            if (startIndex == -1) {
                return null;
            }
            
            startIndex += searchKey.length();
            int endIndex = json.indexOf(",", startIndex);
            if (endIndex == -1) {
                endIndex = json.indexOf("}", startIndex);
            }
            
            if (endIndex == -1) {
                return null;
            }
            
            String priceStr = json.substring(startIndex, endIndex).trim();
            // 移除可能的引号
            if (priceStr.startsWith("\"") && priceStr.endsWith("\"")) {
                priceStr = priceStr.substring(1, priceStr.length() - 1);
            }
            
            return priceStr;
            
        } catch (Exception e) {
            System.out.println("价格解析错误: " + e.getMessage());
            return null;
        }
    }
}
