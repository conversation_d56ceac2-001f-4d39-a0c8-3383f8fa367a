@echo off
echo ==================================================
echo         PUMP30 环境变量配置工具
echo ==================================================
echo.

echo [信息] 正在设置Java UTF-8环境变量...

REM 设置当前会话的环境变量
set JAVA_TOOL_OPTIONS=-Dfile.encoding=UTF-8 -Dconsole.encoding=UTF-8 -Dsun.jnu.encoding=UTF-8

echo [完成] 当前会话已配置UTF-8编码
echo.
echo [提示] 现在您可以直接使用以下命令启动:
echo   java -jar pump30.jar
echo.
echo [注意] 这个设置只对当前命令窗口有效
echo       如果要永久设置，请使用管理员权限运行此脚本
echo.

echo 测试启动 PUMP30...
echo 按任意键开始测试，或按 Ctrl+C 取消
pause >nul

REM 设置控制台代码页
chcp 65001 >nul

echo 启动中...
java -jar pump30.jar 