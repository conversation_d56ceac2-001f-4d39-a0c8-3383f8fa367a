# Jupiter API 调研文档

## 📋 调研目标

解决当前系统中Jupiter API集成的价格选择策略问题：
- **问题**: 当前系统错误地把买入价当做卖出价
- **根因**: 仅基于高低价对比选择价格，未正确使用Jupiter的专门买入/卖出API端点
- **影响**: DEX的买入/卖出价差较大，影响套利分析准确性

## 🔍 Jupiter API 端点分析

### 1. Price API V2 (推荐用于获取买入/卖出价格)

**端点**: `https://api.jup.ag/price/v2`

**用途**: 获取代币的实时价格信息，包括专门的买入和卖出价格

**参数**:
- `ids`: 代币地址 (例: PUMP代币地址)
- `vsToken`: 对比代币地址 (例: USDT地址)

**响应格式**:
```json
{
  "data": {
    "PUMP_TOKEN_ADDRESS": {
      "id": "PUMP_TOKEN_ADDRESS",
      "type": "derivedPrice",
      "price": "132.280970000",
      "extraInfo": {
        "lastSwappedPrice": {
          "lastJupiterSellAt": 1726231876,
          "lastJupiterSellPrice": "132.29239989531536",
          "lastJupiterBuyAt": 1726231877,
          "lastJupiterBuyPrice": "132.19714417319207"
        },
        "quotedPrice": {
          "buyPrice": "132.286960000",
          "buyAt": 1726231878,
          "sellPrice": "132.169110000",
          "sellAt": 1726232168
        }
      }
    }
  }
}
```

**关键字段**:
- `extraInfo.quotedPrice.buyPrice`: 买入价格
- `extraInfo.quotedPrice.sellPrice`: 卖出价格
- `extraInfo.quotedPrice.buyAt`: 买入价格时间戳
- `extraInfo.quotedPrice.sellAt`: 卖出价格时间戳

### 2. Quote API (用于获取具体交易报价)

**端点**: `https://quote-api.jup.ag/v6/quote`

**用途**: 获取具体数量的交易报价，更适合大额交易

**参数**:
- `inputMint`: 输入代币地址
- `outputMint`: 输出代币地址  
- `amount`: 交易数量 (以最小单位表示)
- `slippageBps`: 滑点容忍度 (基点，例如50表示0.5%)

**买入PUMP示例**:
```
GET /quote?inputMint=EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v&outputMint=PUMP_TOKEN_ADDRESS&amount=1000000&slippageBps=50
```

**卖出PUMP示例**:
```
GET /quote?inputMint=PUMP_TOKEN_ADDRESS&outputMint=EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v&amount=100000000000&slippageBps=50
```

**响应格式**:
```json
{
  "inputMint": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
  "outputMint": "PUMP_TOKEN_ADDRESS",
  "inAmount": "1000000",
  "outAmount": "16198753",
  "otherAmountThreshold": "16038927",
  "swapMode": "ExactIn",
  "slippageBps": 50,
  "platformFee": null,
  "priceImpactPct": "0.0001",
  "routePlan": [...]
}
```

## 🎯 推荐解决方案

### 方案1: 使用Price API V2 (推荐)

**优势**:
- 直接提供 `buyPrice` 和 `sellPrice`
- 响应速度快，数据准确
- 适合实时监控场景

**实现要点**:
1. 使用 `https://api.jup.ag/price/v2` 端点
2. 从 `extraInfo.quotedPrice` 中提取买入/卖出价格
3. 处理价格时间戳，确保数据时效性

### 方案2: 使用Quote API (备选)

**优势**:
- 提供具体数量的精确报价
- 包含滑点和价格影响信息
- 适合大额交易场景

**实现要点**:
1. 分别调用两次Quote API
2. 买入：USDT → PUMP
3. 卖出：PUMP → USDT
4. 根据响应计算实际价格

## 📊 代币地址配置

### 已知代币地址
- **USDT**: `EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v`
- **SOL**: `So11111111111111111111111111111111111111112`

### PUMP代币地址
> **重要**: 需要更新为PUMP代币的真实地址
- **当前配置**: `6n7HqLwHMfFn9SYcN8AVVrrZGJHPUUYJGEFUGKo7B7d` (示例地址)
- **获取方式**: 从Gate.io或其他可信来源获取PUMP代币的正确Solana地址

## 🔧 实现建议

### 1. 主要实现 (Price API V2)
```java
public PriceData getPumpPrice() {
    String url = String.format("https://api.jup.ag/price/v2?ids=%s&vsToken=%s", 
                              PUMP_TOKEN, USDT_TOKEN);
    
    JsonNode response = callApi(url);
    JsonNode tokenData = response.get("data").get(PUMP_TOKEN);
    JsonNode quotedPrice = tokenData.get("extraInfo").get("quotedPrice");
    
    BigDecimal buyPrice = new BigDecimal(quotedPrice.get("buyPrice").asText());
    BigDecimal sellPrice = new BigDecimal(quotedPrice.get("sellPrice").asText());
    
    return new PriceData("Jupiter", buyPrice, sellPrice);
}
```

### 2. 备选实现 (Quote API)
```java
public BigDecimal getBuyPrice(BigDecimal usdtAmount) {
    String url = String.format("https://quote-api.jup.ag/v6/quote?inputMint=%s&outputMint=%s&amount=%s&slippageBps=50",
                              USDT_TOKEN, PUMP_TOKEN, convertToSmallestUnit(usdtAmount));
    
    JsonNode response = callApi(url);
    BigDecimal outAmount = new BigDecimal(response.get("outAmount").asText());
    BigDecimal inAmount = new BigDecimal(response.get("inAmount").asText());
    
    // 计算单价: 输入USDT / 输出PUMP
    return inAmount.divide(outAmount, 6, RoundingMode.HALF_UP);
}

public BigDecimal getSellPrice(BigDecimal pumpAmount) {
    String url = String.format("https://quote-api.jup.ag/v6/quote?inputMint=%s&outputMint=%s&amount=%s&slippageBps=50",
                              PUMP_TOKEN, USDT_TOKEN, convertToSmallestUnit(pumpAmount));
    
    JsonNode response = callApi(url);
    BigDecimal outAmount = new BigDecimal(response.get("outAmount").asText());
    BigDecimal inAmount = new BigDecimal(response.get("inAmount").asText());
    
    // 计算单价: 输出USDT / 输入PUMP
    return outAmount.divide(inAmount, 6, RoundingMode.HALF_UP);
}
```

## 🚨 注意事项

### 1. 代币精度处理
- USDT精度: 6位小数
- SOL精度: 9位小数
- PUMP精度: 需要确认 (通常为6或9位)

### 2. 错误处理
- API限流处理
- 网络异常重试
- 数据格式验证

### 3. 性能优化
- 合理的请求间隔
- 响应缓存机制
- 并发请求控制

## 📝 测试计划

### 1. 单元测试
- 测试Price API V2的响应解析
- 测试Quote API的价格计算
- 测试错误情况处理

### 2. 集成测试
- 验证买入/卖出价格的合理性
- 测试价格差异分析
- 验证系统整体表现

### 3. 性能测试
- API响应时间测试
- 并发请求测试
- 长时间运行稳定性测试

## 🔄 下一步行动

1. **立即执行**: 修正JupiterApiClient实现，使用Price API V2
2. **验证测试**: 运行系统验证买入/卖出价格分离
3. **优化改进**: 根据实际表现调整参数和策略

---

**文档版本**: 1.0  
**调研日期**: 2025-01-15  
**调研人员**: AI Agent  
**参考文档**: https://dev.jup.ag/docs/ 