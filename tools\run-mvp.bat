@echo off
chcp 65001 >nul
echo PUMP价格监控系统MVP启动脚本
echo =====================================

REM 检查Java环境
java -version > nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Java环境，请安装Java 8或更高版本
    pause
    exit /b 1
)

REM 检查Maven环境
mvn -version > nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Maven环境，请安装Maven 3.x
    echo 或者下载Maven并配置到PATH环境变量中
    pause
    exit /b 1
)

echo 正在编译项目...
mvn clean compile

if %errorlevel% neq 0 (
    echo 编译失败，请检查代码
    pause
    exit /b 1
)

echo 正在运行PUMP价格监控系统MVP...
mvn spring-boot:run

pause 