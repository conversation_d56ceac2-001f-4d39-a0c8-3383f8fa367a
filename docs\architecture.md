# PUMP代币价格监控系统 - 系统架构文档

## 📋 目录
- [系统概述](#系统概述)
- [整体架构](#整体架构)
- [核心组件](#核心组件)
- [数据流向](#数据流向)
- [技术栈](#技术栈)
- [部署架构](#部署架构)
- [接口依赖](#接口依赖)

---

## 🎯 系统概述

PUMP代币价格监控系统是一个基于Spring Boot的实时价格监控和套利分析系统，专门用于监控PUMP代币在中心化交易所(Gate.io)和去中心化交易所(Jupiter DEX)之间的价格差异，并在发现套利机会时触发音频告警。

### 核心功能
- **实时价格监控**: 每2秒获取100万PUMP代币的CEX和DEX价格
- **套利分析**: 计算买入/卖出价格差异，识别套利机会
- **智能告警**: 基于阈值的音频告警系统，支持自定义音频文件
- **配置管理**: JSON配置文件内嵌到JAR，支持运行时参数调整
- **缓存优化**: 价格数据缓存机制，减少API调用频率

### 业务特性
- **监控标的**: 100万个PUMP代币 (地址: `pumpCmXqMfrsAkQ5r49WcJnRayYRqmXz6ae8H7H9Dfn`)
- **价格范围**: 预期总价格在$10-20 USDT区间
- **告警阈值**: 买入/卖出差价超过$30时触发告警
- **监控频率**: 2秒间隔，符合Jupiter API免费版限制

---

## 🏗️ 整体架构

### 系统架构图
```mermaid
graph TB
    subgraph "PUMP价格监控系统"
        A[PumpApplication<br/>主程序启动] --> B[PriceMonitorScheduler<br/>定时调度器]
        B --> C[PumpPriceService<br/>价格服务层]
        
        C --> D[GateIoApiClient<br/>CEX价格客户端]
        C --> E[JupiterApiClientFixed<br/>DEX价格客户端]
        C --> F[JupiterUltraApiClient<br/>Ultra API客户端]
        
        B --> G[AlertSoundService<br/>音频告警服务]
        
        H[PumpConfigService<br/>配置管理] --> B
        H --> G
        
        I[PriceCache<br/>价格缓存] --> E
        I --> F
        
        J[EncodingInitializer<br/>编码初始化] --> A
    end
    
    subgraph "外部API"
        K[Gate.io API<br/>订单簿数据]
        L[Jupiter Quote API<br/>DEX报价]
        M[Jupiter Price API<br/>价格数据]
        N[Jupiter Ultra API<br/>增强路由]
    end
    
    subgraph "配置与资源"
        O[application.properties<br/>Spring配置]
        P[pump-config.json<br/>业务配置]
        Q[up.wav / down.wav<br/>告警音频]
    end
    
    D --> K
    E --> L
    E --> M
    F --> N
    
    H --> P
    A --> O
    G --> Q
    
    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style G fill:#fff3e0
```

---

## 🔧 核心组件

### 1. 主程序模块 (PumpApplication)
**职责**: Spring Boot应用启动和系统初始化
<augment_code_snippet path="src/main/java/com/pump/PumpApplication.java" mode="EXCERPT">
````java
@SpringBootApplication
@EnableScheduling
public class PumpApplication {
    static {
        EncodingInitializer.forceInitializeUTF8();
    }
    
    public static void main(String[] args) {
        configureSystemEncoding();
        SpringApplication app = new SpringApplication(PumpApplication.class);
        app.setLogStartupInfo(false);
        app.run(args);
    }
}
````
</augment_code_snippet>

**关键特性**:
- UTF-8编码强制初始化
- 禁用Spring Boot启动横幅
- 时区设置为Asia/Shanghai

### 2. 定时调度器 (PriceMonitorScheduler)
**职责**: 核心业务逻辑调度和价格监控输出
<augment_code_snippet path="src/main/java/com/pump/scheduler/PriceMonitorScheduler.java" mode="EXCERPT">
````java
@Scheduled(fixedDelay = 1000)
public void monitorPrices() {
    long currentTime = System.currentTimeMillis();
    if (currentTime - lastExecutionTime < configService.getMonitorInterval()) {
        return;
    }
    lastExecutionTime = currentTime;
    
    outputSimplifiedPrices();
}
````
</augment_code_snippet>

**核心功能**:
- 动态间隔控制 (默认2秒)
- 格式化价格输出
- 告警触发逻辑

### 3. 价格服务层 (PumpPriceService)
**职责**: 统一的价格数据获取接口
<augment_code_snippet path="src/main/java/com/pump/service/impl/PumpPriceServiceImpl.java" mode="EXCERPT">
````java
@Service
public class PumpPriceServiceImpl implements PumpPriceService {
    @Autowired
    private GateIoApiClient gateIoApiClient;
    @Autowired
    private JupiterApiClientFixed jupiterApiClient;
    @Autowired
    private JupiterUltraApiClient jupiterUltraApiClient;
    
    public PriceData getCexPrice() {
        return gateIoApiClient.getPumpPrice();
    }
}
````
</augment_code_snippet>

### 4. API客户端模块

#### Gate.io API客户端
**职责**: 获取CEX订单簿价格数据
<augment_code_snippet path="src/main/java/com/pump/client/GateIoApiClient.java" mode="EXCERPT">
````java
public PriceData getPumpPrice() {
    String url = String.format("%s/spot/tickers?currency_pair=%s", baseUrl, SYMBOL);
    String response = restTemplate.getForObject(url, String.class);
    return parseTickerResponse(response);
}
````
</augment_code_snippet>

#### Jupiter API客户端
**职责**: 获取DEX价格和报价数据
<augment_code_snippet path="src/main/java/com/pump/client/JupiterApiClientFixed.java" mode="EXCERPT">
````java
public BigDecimal getQuotePrice(BigDecimal amount, boolean isBuy) {
    if (isBuy) {
        inputMint = "USDT";
        outputMint = "PUMP";
        inputAmount = amount.multiply(basePrice.getLastPrice()).multiply(new BigDecimal("1.2"));
    } else {
        inputMint = "PUMP";
        outputMint = "USDT";
        inputAmount = amount;
    }
}
````
</augment_code_snippet>

### 5. 音频告警系统 (AlertSoundService)
**职责**: 基于价格差异阈值的音频告警
<augment_code_snippet path="src/main/java/com/pump/service/AlertSoundService.java" mode="EXCERPT">
````java
public void checkBuyAlert(BigDecimal buyDifference, BigDecimal jupiterPrice, BigDecimal gatePrice) {
    if (buyDifference.compareTo(buyThreshold) > 0 && !isBuyAudioPlaying) {
        triggerAlert(AlertType.BUY_OPPORTUNITY, 
            String.format("买入机会: 差价$%.2f", buyDifference));
    }
}
````
</augment_code_snippet>

**告警类型**:
- **买入告警**: up.wav (差价 > $30)
- **卖出告警**: down.wav (差价 > $30)
- **冷却机制**: 30秒防重复播放

### 6. 配置管理系统 (PumpConfigService)
**职责**: JSON配置文件管理和参数获取
<augment_code_snippet path="src/main/java/com/pump/config/PumpConfigService.java" mode="EXCERPT">
````java
@PostConstruct
public void loadConfig() {
    ClassPathResource resource = new ClassPathResource(CONFIG_FILE);
    if (resource.exists()) {
        config = objectMapper.readTree(resource.getInputStream());
    }
}
````
</augment_code_snippet>

**配置项**:
- 监控间隔: 200ms - 2000ms
- 告警阈值: $30 (买入/卖出)
- API超时: 30秒
- 代理设置: 可选SOCKS/HTTP

---

## 📊 数据流向

### 价格监控数据流
```mermaid
sequenceDiagram
    participant S as PriceMonitorScheduler
    participant PS as PumpPriceService
    participant G as GateIoApiClient
    participant J as JupiterApiClient
    participant A as AlertSoundService
    participant C as PriceCache
    
    S->>PS: getCexPrice()
    PS->>G: getPumpPrice()
    G->>G: 调用Gate.io API
    G-->>PS: PriceData(CEX价格)
    
    S->>PS: getDexBuyPrice(1000000)
    PS->>J: getQuotePrice(1000000, true)
    J->>C: 检查缓存
    alt 缓存未命中
        J->>J: 调用Jupiter Quote API
        J->>C: 更新缓存
    end
    J-->>PS: BigDecimal(买入单价)
    
    S->>PS: getDexSellPrice(1000000)
    PS->>J: getQuotePrice(1000000, false)
    J-->>PS: BigDecimal(卖出单价)
    
    S->>S: 计算价格差异
    S->>A: checkBuyAlert(差价)
    S->>A: checkSellAlert(差价)
    
    alt 差价超过阈值
        A->>A: 播放告警音频
    end
    
    S->>S: 输出格式化价格信息
```

### 配置加载流程
```mermaid
graph LR
    A[应用启动] --> B[EncodingInitializer]
    B --> C[PumpConfigService.loadConfig]
    C --> D{pump-config.json存在?}
    D -->|是| E[加载JSON配置]
    D -->|否| F[使用默认配置]
    E --> G[验证配置参数]
    F --> G
    G --> H[配置生效]
```

---

## 💻 技术栈

### 核心框架
- **Spring Boot 2.7.0**: 应用框架和依赖注入
- **Spring Scheduling**: 定时任务调度
- **Maven 3.8+**: 项目构建和依赖管理

### HTTP客户端
- **RestTemplate**: Gate.io API调用
- **HttpURLConnection**: Jupiter API调用 (原生实现)

### JSON处理
- **Jackson ObjectMapper**: JSON解析和序列化
- **JsonNode**: 动态JSON数据处理

### 日志系统
- **SLF4J**: 日志门面
- **Logback**: 日志实现 (Spring Boot默认)

### 音频处理
- **Java Sound API**: 音频文件播放
- **CompletableFuture**: 异步音频播放

### 数据处理
- **BigDecimal**: 高精度价格计算
- **LocalDateTime**: 时间处理
- **UTF-8编码**: 中文字符支持

---

## 🚀 部署架构

### JAR包部署
```
pump30.jar
├── BOOT-INF/
│   ├── classes/
│   │   ├── application.properties
│   │   ├── pump-config.json
│   │   ├── up.wav
│   │   ├── down.wav
│   │   └── com/pump/...
│   └── lib/
│       └── [依赖JAR文件]
├── META-INF/
└── org/springframework/boot/loader/
```

### 启动脚本
- **Windows**: `pump.bat`, `Start-Pump30.ps1`
- **跨平台**: `start-pump-utf8.ps1`

### 系统要求
- **Java版本**: JDK 11+
- **内存要求**: 最小512MB
- **网络要求**: 访问Gate.io和Jupiter API
- **操作系统**: Windows/Linux/macOS

---

## 🔗 接口依赖

### Gate.io API
- **基础URL**: `https://api.gateio.ws/api/v4`
- **使用接口**: 
  - `/spot/tickers` - 获取PUMP_USDT价格
  - `/spot/order_book` - 获取订单簿深度
- **限制**: 无明确限制，建议控制频率

### Jupiter API
- **Quote API**: `https://quote-api.jup.ag/v6/quote`
- **Price API**: `https://price.jup.ag/v4/price`
- **Ultra API**: `https://ultra-api.jup.ag/v1/quote`
- **限制**: 免费版每分钟60次请求

### 配置参数映射
```json
{
  "monitor": {
    "interval": 2000,
    "amount": 1000000
  },
  "alert": {
    "buyThreshold": 30.00,
    "sellThreshold": 30.00,
    "soundType": "CUSTOM"
  }
}
```

---

## 🔄 系统交互流程

### 完整监控周期流程图
```mermaid
sequenceDiagram
    participant Timer as 定时器
    participant Scheduler as PriceMonitorScheduler
    participant PriceService as PumpPriceService
    participant GateClient as GateIoApiClient
    participant JupiterClient as JupiterApiClientFixed
    participant UltraClient as JupiterUltraApiClient
    participant AlertService as AlertSoundService
    participant Cache as PriceCache
    participant Config as PumpConfigService

    Timer->>Scheduler: 每2秒触发
    Scheduler->>Config: getMonitorInterval()
    Config-->>Scheduler: 2000ms

    Scheduler->>PriceService: getCexPrice()
    PriceService->>GateClient: getPumpPrice()
    GateClient->>GateClient: 调用Gate.io API
    GateClient-->>PriceService: PriceData(单价)
    PriceService-->>Scheduler: PriceData

    Scheduler->>PriceService: getDexBuyPrice(1000000)
    PriceService->>JupiterClient: getQuotePrice(1000000, true)
    JupiterClient->>Cache: 检查价格缓存
    alt 缓存命中
        Cache-->>JupiterClient: 缓存的基础价格
    else 缓存未命中
        JupiterClient->>JupiterClient: 调用Price API
        JupiterClient->>Cache: 更新缓存
    end
    JupiterClient->>JupiterClient: 调用Quote API (买入)
    JupiterClient-->>PriceService: BigDecimal(买入单价)
    PriceService-->>Scheduler: BigDecimal(买入总价)

    Scheduler->>PriceService: getDexSellPrice(1000000)
    PriceService->>UltraClient: getUltraQuotePrice(1000000, false)
    UltraClient->>UltraClient: 调用Ultra API
    UltraClient-->>PriceService: BigDecimal(卖出单价)
    PriceService-->>Scheduler: BigDecimal(卖出总价)

    Scheduler->>Scheduler: 计算买入差价
    Scheduler->>Scheduler: 计算卖出差价

    Scheduler->>AlertService: checkBuyAlert(buyDifference)
    AlertService->>Config: getBuyThreshold()
    Config-->>AlertService: 30.00
    alt 差价 > 阈值
        AlertService->>AlertService: 播放up.wav
    end

    Scheduler->>AlertService: checkSellAlert(sellDifference)
    AlertService->>Config: getSellThreshold()
    Config-->>AlertService: 30.00
    alt 差价 > 阈值
        AlertService->>AlertService: 播放down.wav
    end

    Scheduler->>Scheduler: 输出格式化价格信息
```

### 配置管理流程图
```mermaid
graph TD
    A[应用启动] --> B[EncodingInitializer.forceInitializeUTF8]
    B --> C[PumpApplication.main]
    C --> D[Spring容器初始化]
    D --> E[PumpConfigService.@PostConstruct]
    E --> F{pump-config.json存在?}
    F -->|是| G[ClassPathResource加载]
    F -->|否| H[createDefaultConfig]
    G --> I[ObjectMapper.readTree]
    H --> I
    I --> J[配置验证]
    J --> K{配置有效?}
    K -->|是| L[logConfigSummary]
    K -->|否| M[抛出RuntimeException]
    L --> N[配置服务就绪]
    M --> O[应用启动失败]

    style A fill:#e1f5fe
    style N fill:#e8f5e8
    style O fill:#ffebee
```

### 缓存机制流程图
```mermaid
graph LR
    A[API请求] --> B{检查缓存}
    B -->|命中| C[返回缓存数据]
    B -->|未命中| D[调用外部API]
    D --> E{API调用成功?}
    E -->|是| F[解析响应数据]
    E -->|否| G[返回错误]
    F --> H[存入缓存]
    H --> I[设置TTL=30s]
    I --> J[返回数据]

    K[定时清理] --> L{检查过期}
    L -->|过期| M[清除缓存项]
    L -->|未过期| N[保留缓存项]

    style C fill:#e8f5e8
    style G fill:#ffebee
    style J fill:#e8f5e8
```

---

## 📊 性能指标与监控

### 系统性能基准
| 指标类型 | 基准值 | 监控方式 | 告警阈值 |
|---------|--------|----------|----------|
| API响应时间 | < 2秒 | 日志记录 | > 5秒 |
| 内存使用 | < 512MB | JVM监控 | > 1GB |
| CPU使用率 | < 20% | 系统监控 | > 50% |
| 监控频率 | 2秒/次 | 配置控制 | 偏差 > 500ms |
| 缓存命中率 | > 70% | 缓存统计 | < 50% |
| 告警延迟 | < 100ms | 音频监控 | > 500ms |

### 资源消耗分析
```mermaid
pie title 系统资源消耗分布
    "API调用" : 40
    "数据处理" : 25
    "音频播放" : 15
    "日志输出" : 10
    "缓存管理" : 5
    "其他" : 5
```

### 监控告警机制
```mermaid
graph TB
    A[性能监控] --> B{指标检查}
    B -->|正常| C[继续监控]
    B -->|异常| D[触发告警]
    D --> E[记录告警日志]
    E --> F[发送通知]
    F --> G[执行恢复策略]
    G --> H{恢复成功?}
    H -->|是| C
    H -->|否| I[升级告警]
    I --> J[人工介入]
```

---

## 🔐 安全性考虑

### API安全策略
- **访问控制**: 无需API密钥的公开接口
- **频率限制**: 遵循各API的调用限制
- **超时控制**: 防止长时间阻塞
- **重试机制**: 指数退避策略
- **错误处理**: 敏感信息过滤

### 数据安全措施
- **内存管理**: 及时释放价格数据
- **日志安全**: 避免记录敏感信息
- **配置保护**: 配置文件内嵌到JAR
- **网络安全**: HTTPS协议通信

### 系统安全架构
```mermaid
graph TD
    A[外部API] --> B[HTTP客户端]
    B --> C[请求验证]
    C --> D[频率控制]
    D --> E[数据处理]
    E --> F[结果缓存]
    F --> G[安全日志]

    H[配置管理] --> I[内嵌配置]
    I --> J[参数验证]
    J --> K[安全存储]

    L[音频系统] --> M[文件验证]
    M --> N[安全播放]

    style C fill:#fff3e0
    style D fill:#fff3e0
    style J fill:#fff3e0
    style M fill:#fff3e0
```

---

## 🚀 扩展性设计

### 水平扩展能力
- **多实例部署**: 支持多个监控实例并行运行
- **负载均衡**: API调用分散到不同实例
- **数据同步**: 通过外部存储同步配置
- **故障转移**: 实例故障时自动切换

### 功能扩展点
```mermaid
graph LR
    A[核心系统] --> B[价格源扩展]
    A --> C[告警方式扩展]
    A --> D[存储扩展]
    A --> E[分析算法扩展]

    B --> B1[新增交易所]
    B --> B2[新增DEX]
    B --> B3[新增代币]

    C --> C1[邮件告警]
    C --> C2[短信告警]
    C --> C3[Webhook通知]

    D --> D1[数据库存储]
    D --> D2[时序数据库]
    D --> D3[云存储]

    E --> E1[机器学习预测]
    E --> E2[高级套利策略]
    E --> E3[风险评估]
```

### 配置扩展示例
```json
{
  "exchanges": {
    "gate": { "enabled": true, "weight": 1.0 },
    "binance": { "enabled": false, "weight": 0.8 },
    "okx": { "enabled": false, "weight": 0.9 }
  },
  "dex": {
    "jupiter": { "enabled": true, "priority": 1 },
    "raydium": { "enabled": false, "priority": 2 },
    "orca": { "enabled": false, "priority": 3 }
  },
  "alerts": {
    "audio": { "enabled": true },
    "email": { "enabled": false, "smtp": "..." },
    "webhook": { "enabled": false, "url": "..." }
  }
}
```

---

## 📈 运维监控

### 日志管理策略
```mermaid
graph TD
    A[应用日志] --> B[日志分级]
    B --> C[ERROR级别]
    B --> D[WARN级别]
    B --> E[INFO级别]
    B --> F[DEBUG级别]

    C --> G[立即告警]
    D --> H[监控统计]
    E --> I[业务分析]
    F --> J[开发调试]

    G --> K[日志存储]
    H --> K
    I --> K
    J --> K

    K --> L[日志轮转]
    L --> M[压缩归档]
    M --> N[定期清理]
```

### 健康检查机制
- **API可用性检查**: 定期验证外部API状态
- **系统资源监控**: CPU、内存、磁盘使用率
- **业务指标监控**: 价格获取成功率、告警触发频率
- **配置有效性检查**: 配置参数合理性验证

### 故障恢复策略
1. **自动重试**: API调用失败时的重试机制
2. **降级服务**: 部分API不可用时的降级策略
3. **缓存兜底**: 使用缓存数据应对临时故障
4. **告警静默**: 避免故障期间的告警风暴

---

**文档版本**: v1.0
**最后更新**: 2025-01-16
**维护者**: AI Agent
