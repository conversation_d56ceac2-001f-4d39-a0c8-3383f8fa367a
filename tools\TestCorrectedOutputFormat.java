import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 测试修正后的输出格式
 * GATE单价也按100万个PUMP的总价显示
 * 
 * <AUTHOR> Agent
 * @version 1.0
 * @since 2025-01-15
 */
public class TestCorrectedOutputFormat {
    
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
    
    public static void main(String[] args) {
        System.out.println("=== 修正后的输出格式测试 ===");
        System.out.println();
        
        // 模拟价格数据
        BigDecimal gateUnitPrice = new BigDecimal("0.005634");  // Gate.io单个PUMP价格
        BigDecimal gateTotalPrice = gateUnitPrice.multiply(new BigDecimal("1000000")); // 100万个PUMP总价
        BigDecimal jupiterBuyTotalPrice = new BigDecimal("5641.95");
        BigDecimal jupiterSellTotalPrice = new BigDecimal("5635.63");
        
        // 计算差价
        BigDecimal buyDifference = jupiterBuyTotalPrice.subtract(gateTotalPrice);
        BigDecimal sellDifference = jupiterSellTotalPrice.subtract(gateTotalPrice);
        
        String timestamp = LocalDateTime.now().format(TIME_FORMATTER);
        
        System.out.println("=== 修正后的格式输出示例 ===");
        System.out.println();
        
        // 第一行：分隔线标题 + GATE总价（100万个PUMP）
        System.out.println(String.format("%s : ===============PUMP监控 ===============GATE单价: $%.2f",
            timestamp, gateTotalPrice));
        
        // 第二行：池买入价格 + 差价 + 做升
        System.out.println(String.format("%s : 池买入100W个PUMP: $%.2f，差价：$%.2f，做升",
            timestamp, 
            jupiterBuyTotalPrice,
            buyDifference));
        
        // 第三行：池卖出价格 + 差价 + 做跌
        System.out.println(String.format("%s : 池卖出100W个PUMP: $%.2f，差价：$%.2f，做跌",
            timestamp,
            jupiterSellTotalPrice,
            sellDifference));
        
        System.out.println();
        System.out.println("=== 修正说明 ===");
        System.out.println("✅ GATE单价：现在显示100万个PUMP的总价 $" + String.format("%.2f", gateTotalPrice));
        System.out.println("✅ 池买入：显示100万个PUMP的总价 $" + String.format("%.2f", jupiterBuyTotalPrice));
        System.out.println("✅ 池卖出：显示100万个PUMP的总价 $" + String.format("%.2f", jupiterSellTotalPrice));
        System.out.println("✅ 所有价格都是基于100万个PUMP的总价格，便于直接对比");
        
        System.out.println();
        System.out.println("=== 价格对比分析 ===");
        System.out.printf("Gate.io单个PUMP价格: $%.6f%n", gateUnitPrice);
        System.out.printf("Gate.io 100万个PUMP总价: $%.2f%n", gateTotalPrice);
        System.out.printf("Ultra API买入100万个PUMP: $%.2f%n", jupiterBuyTotalPrice);
        System.out.printf("Ultra API卖出100万个PUMP: $%.2f%n", jupiterSellTotalPrice);
        System.out.println();
        System.out.printf("买入差价: $%.2f (%s)%n", buyDifference, 
            buyDifference.compareTo(BigDecimal.ZERO) > 0 ? "Ultra买入成本更高" : "Ultra买入成本更低");
        System.out.printf("卖出差价: $%.2f (%s)%n", sellDifference, 
            sellDifference.compareTo(BigDecimal.ZERO) > 0 ? "Ultra卖出收入更高" : "Ultra卖出收入更低");
        
        System.out.println();
        System.out.println("=== 套利分析 ===");
        if (buyDifference.compareTo(BigDecimal.ZERO) < 0) {
            System.out.println("🔥 买入机会：Ultra API买入成本比Gate.io低 $" + String.format("%.2f", buyDifference.abs()));
        }
        if (sellDifference.compareTo(BigDecimal.ZERO) > 0) {
            System.out.println("🔥 卖出机会：Ultra API卖出收入比Gate.io高 $" + String.format("%.2f", sellDifference));
        }
        
        System.out.println();
        System.out.println("现在所有价格都是统一的100万个PUMP总价格，便于直接对比！");
    }
}
