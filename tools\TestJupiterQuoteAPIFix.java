import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.Proxy;
import java.net.InetSocketAddress;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 测试Jupiter Quote API修复
 * 验证API响应解析和价格计算是否正确
 * 
 * <AUTHOR> Agent
 * @version 1.0
 * @since 2025-01-15
 */
public class TestJupiterQuoteAPIFix {
    
    // 正确的代币地址
    private static final String PUMP_TOKEN = "pumpCmXqMfrsAkQ5r49WcJnRayYRqmXz6ae8H7H9Dfn";
    private static final String USDT_TOKEN = "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB";
    
    public static void main(String[] args) {
        System.out.println("=== 测试Jupiter Quote API修复 ===");
        System.out.println();
        
        // 测试100万个PUMP的Quote API调用
        testQuoteAPI();
        
        System.out.println("\n=== 测试完成 ===");
    }
    
    /**
     * 测试Jupiter Quote API
     */
    private static void testQuoteAPI() {
        try {
            BigDecimal amount = new BigDecimal("1000000"); // 100万个PUMP
            BigDecimal amountInSmallestUnit = amount.multiply(new BigDecimal("1000000")); // 转换为最小单位
            
            // 构建Quote API URL
            String quoteUrl = "https://lite-api.jup.ag/swap/v1/quote";
            String url = String.format("%s?inputMint=%s&outputMint=%s&amount=%s&slippageBps=50",
                                     quoteUrl, PUMP_TOKEN, USDT_TOKEN, amountInSmallestUnit.toBigInteger());
            
            System.out.println("测试Jupiter Quote API调用:");
            System.out.println("URL: " + url);
            System.out.println("参数:");
            System.out.println("  - inputMint: " + PUMP_TOKEN + " (PUMP)");
            System.out.println("  - outputMint: " + USDT_TOKEN + " (USDT)");
            System.out.println("  - amount: " + amountInSmallestUnit.toBigInteger() + " (100万个PUMP的最小单位)");
            System.out.println();
            
            // 发送HTTP请求（使用代理）
            URL urlObj = new URL(url);
            Proxy proxy = new Proxy(Proxy.Type.SOCKS, new InetSocketAddress("127.0.0.1", 7890));
            HttpURLConnection connection = (HttpURLConnection) urlObj.openConnection(proxy);
            
            connection.setRequestMethod("GET");
            connection.setRequestProperty("User-Agent", "PUMP-Monitor/1.0");
            connection.setRequestProperty("Accept", "application/json");
            connection.setConnectTimeout(15000);
            connection.setReadTimeout(20000);
            
            System.out.println("正在发送请求...");
            int responseCode = connection.getResponseCode();
            System.out.println("响应代码: " + responseCode);
            
            if (responseCode == 200) {
                // 读取响应
                BufferedReader reader = new BufferedReader(
                    new InputStreamReader(connection.getInputStream()));
                StringBuilder response = new StringBuilder();
                String line;
                
                while ((line = reader.readLine()) != null) {
                    response.append(line);
                }
                reader.close();
                
                String responseBody = response.toString();
                System.out.println("响应内容: " + responseBody);
                System.out.println();
                
                // 解析响应并计算价格
                parseAndCalculatePrice(responseBody, amount);
                
            } else {
                System.out.println("❌ API请求失败，响应代码: " + responseCode);
                
                // 读取错误信息
                try {
                    BufferedReader errorReader = new BufferedReader(
                        new InputStreamReader(connection.getErrorStream()));
                    StringBuilder errorResponse = new StringBuilder();
                    String errorLine;
                    
                    while ((errorLine = errorReader.readLine()) != null) {
                        errorResponse.append(errorLine);
                    }
                    errorReader.close();
                    
                    System.out.println("错误信息: " + errorResponse.toString());
                } catch (Exception e) {
                    System.out.println("无法读取错误信息: " + e.getMessage());
                }
            }
            
        } catch (Exception e) {
            System.out.println("❌ 测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 解析响应并计算价格
     */
    private static void parseAndCalculatePrice(String responseBody, BigDecimal pumpAmount) {
        try {
            // 简单的JSON解析
            String inAmountKey = "\"inAmount\":\"";
            String outAmountKey = "\"outAmount\":\"";
            
            int inStartIndex = responseBody.indexOf(inAmountKey);
            int outStartIndex = responseBody.indexOf(outAmountKey);
            
            if (inStartIndex == -1 || outStartIndex == -1) {
                System.out.println("❌ 响应缺少inAmount或outAmount字段");
                return;
            }
            
            // 解析inAmount (PUMP最小单位)
            inStartIndex += inAmountKey.length();
            int inEndIndex = responseBody.indexOf("\"", inStartIndex);
            String inAmountStr = responseBody.substring(inStartIndex, inEndIndex);
            BigDecimal inAmount = new BigDecimal(inAmountStr);
            
            // 解析outAmount (USDT最小单位)
            outStartIndex += outAmountKey.length();
            int outEndIndex = responseBody.indexOf("\"", outStartIndex);
            String outAmountStr = responseBody.substring(outStartIndex, outEndIndex);
            BigDecimal outAmount = new BigDecimal(outAmountStr);
            
            System.out.println("=== 响应数据解析 ===");
            System.out.println("inAmount (PUMP最小单位): " + inAmount);
            System.out.println("outAmount (USDT最小单位): " + outAmount);
            
            // 转换为正常单位
            BigDecimal pumpNormal = inAmount.divide(new BigDecimal("1000000"), 8, RoundingMode.HALF_UP);
            BigDecimal usdtNormal = outAmount.divide(new BigDecimal("1000000"), 8, RoundingMode.HALF_UP);
            
            System.out.println("PUMP正常单位: " + pumpNormal);
            System.out.println("USDT正常单位: " + usdtNormal);
            
            // 计算单价
            BigDecimal unitPrice = usdtNormal.divide(pumpNormal, 8, RoundingMode.HALF_UP);
            System.out.println("单价: " + unitPrice + " USDT/PUMP");
            
            // 验证修复后的逻辑
            System.out.println();
            System.out.println("=== 修复后的价格计算逻辑验证 ===");
            System.out.println("1. getQuotePrice()应该返回单价: " + unitPrice + " USDT/PUMP");
            System.out.println("2. 价格服务应该计算总价: " + unitPrice + " × " + pumpAmount + " = " + 
                              unitPrice.multiply(pumpAmount) + " USDT");
            System.out.println("3. 最终输出应该显示: " + String.format("%.2f", unitPrice.multiply(pumpAmount)) + " USDT");
            
            // 验证价格合理性
            BigDecimal expectedTotal = new BigDecimal("5800");
            BigDecimal actualTotal = unitPrice.multiply(pumpAmount);
            BigDecimal tolerance = new BigDecimal("1000");
            
            boolean isReasonable = actualTotal.subtract(expectedTotal).abs().compareTo(tolerance) <= 0;
            System.out.println();
            System.out.println("价格合理性验证: " + (isReasonable ? "✅ 通过" : "❌ 异常"));
            System.out.println("预期总价: ~" + expectedTotal + " USDT");
            System.out.println("实际总价: " + actualTotal + " USDT");
            
            if (isReasonable) {
                System.out.println("\n🎉 修复成功！Jupiter Quote API现在应该返回正确的价格");
            } else {
                System.out.println("\n⚠️ 价格仍然异常，需要进一步检查");
            }
            
        } catch (Exception e) {
            System.out.println("❌ 响应解析失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
