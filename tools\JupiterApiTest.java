import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.InetSocketAddress;
import java.net.Proxy;

/**
 * Jupiter API V3连接测试
 * 用于验证API连接状态和代理配置
 */
public class JupiterApiTest {
    
    private static final String JUPITER_API_URL = "https://lite-api.jup.ag/price/v3";
    private static final String PUMP_TOKEN = "83cXjb9Z8iJSBK2DBacte1utUsygdotMBQVhJoSppump";
    
    public static void main(String[] args) {
        System.out.println("🔍 Jupiter API V3 连接测试");
        System.out.println("目标API: " + JUPITER_API_URL);
        System.out.println("代币地址: " + PUMP_TOKEN);
        System.out.println("----------------------------------------");
        
        // 测试1：直接连接
        testDirectConnection();
        
        // 测试2：使用代理连接
        testProxyConnection();
        
        // 测试3：使用系统代理
        testSystemProxy();
    }
    
    /**
     * 测试直接连接
     */
    private static void testDirectConnection() {
        System.out.println("\n📡 测试1: 直接连接");
        try {
            String fullUrl = JUPITER_API_URL + "?ids=" + PUMP_TOKEN;
            URL url = new URL(fullUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            
            // 设置请求属性
            connection.setRequestMethod("GET");
            connection.setRequestProperty("Accept", "application/json");
            connection.setRequestProperty("User-Agent", "PUMP-Monitor/1.0");
            connection.setConnectTimeout(30000);
            connection.setReadTimeout(30000);
            
            int responseCode = connection.getResponseCode();
            System.out.println("响应码: " + responseCode);
            
            if (responseCode == 200) {
                BufferedReader reader = new BufferedReader(
                    new InputStreamReader(connection.getInputStream()));
                String line;
                StringBuilder response = new StringBuilder();
                
                while ((line = reader.readLine()) != null) {
                    response.append(line);
                }
                reader.close();
                
                System.out.println("✅ 直接连接成功!");
                System.out.println("响应数据: " + response.toString());
            } else {
                System.out.println("❌ 直接连接失败，响应码: " + responseCode);
            }
            
        } catch (Exception e) {
            System.out.println("❌ 直接连接异常: " + e.getMessage());
        }
    }
    
    /**
     * 测试代理连接
     */
    private static void testProxyConnection() {
        System.out.println("\n🔄 测试2: 代理连接 (127.0.0.1:7890)");
        try {
            String fullUrl = JUPITER_API_URL + "?ids=" + PUMP_TOKEN;
            URL url = new URL(fullUrl);
            
            // 配置SOCKS代理
            Proxy proxy = new Proxy(Proxy.Type.SOCKS, 
                new InetSocketAddress("127.0.0.1", 7890));
            
            HttpURLConnection connection = (HttpURLConnection) url.openConnection(proxy);
            
            // 设置请求属性
            connection.setRequestMethod("GET");
            connection.setRequestProperty("Accept", "application/json");
            connection.setRequestProperty("User-Agent", "PUMP-Monitor/1.0");
            connection.setConnectTimeout(30000);
            connection.setReadTimeout(30000);
            
            int responseCode = connection.getResponseCode();
            System.out.println("响应码: " + responseCode);
            
            if (responseCode == 200) {
                BufferedReader reader = new BufferedReader(
                    new InputStreamReader(connection.getInputStream()));
                String line;
                StringBuilder response = new StringBuilder();
                
                while ((line = reader.readLine()) != null) {
                    response.append(line);
                }
                reader.close();
                
                System.out.println("✅ 代理连接成功!");
                System.out.println("响应数据: " + response.toString());
            } else {
                System.out.println("❌ 代理连接失败，响应码: " + responseCode);
            }
            
        } catch (Exception e) {
            System.out.println("❌ 代理连接异常: " + e.getMessage());
        }
    }
    
    /**
     * 测试系统代理
     */
    private static void testSystemProxy() {
        System.out.println("\n🌐 测试3: 系统代理设置");
        try {
            // 设置系统代理属性
            System.setProperty("http.proxyHost", "127.0.0.1");
            System.setProperty("http.proxyPort", "7890");
            System.setProperty("https.proxyHost", "127.0.0.1");
            System.setProperty("https.proxyPort", "7890");
            
            String fullUrl = JUPITER_API_URL + "?ids=" + PUMP_TOKEN;
            URL url = new URL(fullUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            
            // 设置请求属性
            connection.setRequestMethod("GET");
            connection.setRequestProperty("Accept", "application/json");
            connection.setRequestProperty("User-Agent", "PUMP-Monitor/1.0");
            connection.setConnectTimeout(30000);
            connection.setReadTimeout(30000);
            
            int responseCode = connection.getResponseCode();
            System.out.println("响应码: " + responseCode);
            
            if (responseCode == 200) {
                BufferedReader reader = new BufferedReader(
                    new InputStreamReader(connection.getInputStream()));
                String line;
                StringBuilder response = new StringBuilder();
                
                while ((line = reader.readLine()) != null) {
                    response.append(line);
                }
                reader.close();
                
                System.out.println("✅ 系统代理连接成功!");
                System.out.println("响应数据: " + response.toString());
            } else {
                System.out.println("❌ 系统代理连接失败，响应码: " + responseCode);
            }
            
        } catch (Exception e) {
            System.out.println("❌ 系统代理连接异常: " + e.getMessage());
        }
    }
} 