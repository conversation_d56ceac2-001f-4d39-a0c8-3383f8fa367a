# 🔧 PUMP30中文显示修复说明

## 🎯 问题描述

在Windows命令行中运行PUMP30时，中文字符显示为乱码：
```
PUMP浠锋牸鐩戞帶绯荤粺宸插惎鍔紝鐩戞帶闂撮殧: 200ms
鎶ヨ閰嶇疆 - 鍚敤: true, 涔板叆闃堝€? $30.00
```

## ✅ 解决方案

### **方案1：使用内置编码的启动脚本** ⭐ **(推荐)**

使用提供的内置UTF-8编码启动脚本：
```bash
.\pump30.bat
```

**特点**：
- ✅ **自动设置编码**：脚本自动配置UTF-8编码
- ✅ **用户友好**：显示启动信息和进度
- ✅ **完全自包含**：无需额外参数

**效果**：
```
PUMP价格监控系统已启动，监控间隔: 200ms
报警配置 - 启用: true, 买入阈值: $30.00, 卖出阈值: $30.00, 音频类型: CUSTOM
2025-07-15 14:26:45.461 : ===============PUMP监控 ===============
GATE单价: $6042.00
池买入100W个PUMP: $6055.54，差价：$-13.54，做升
池卖出100W个PUMP: $6049.16，差价：$7.16，做跌
```

### **方案2：手动设置编码**

如果需要手动运行，使用以下命令：
```bash
# 设置控制台编码为UTF-8
chcp 65001

# 运行程序
java -Dfile.encoding=UTF-8 -Dconsole.encoding=UTF-8 -jar pump30.jar
```

### **方案3：PowerShell运行**

在PowerShell中运行（通常有更好的UTF-8支持）：
```powershell
java -Dfile.encoding=UTF-8 -Dconsole.encoding=UTF-8 -jar pump30.jar
```

## 🔧 技术原理

### **编码问题原因**
1. **Windows默认编码**：Windows命令行默认使用GBK编码
2. **Java输出编码**：Java程序输出UTF-8编码的中文
3. **编码不匹配**：GBK解析UTF-8导致乱码

### **解决方案原理**
1. **设置控制台编码**：`chcp 65001` 将控制台设置为UTF-8
2. **设置Java编码**：`-Dfile.encoding=UTF-8` 确保Java使用UTF-8
3. **设置控制台输出编码**：`-Dconsole.encoding=UTF-8` 确保输出编码正确

## 📁 文件说明

### **启动脚本**
- `run-utf8.bat` - UTF-8启动脚本（推荐使用）
- `run-pump30.bat` - 原始启动脚本（可能有乱码）
- `start-pump30-utf8.bat` - 详细版UTF-8脚本

### **配置文件**
- `src/main/resources/application.properties` - 已添加UTF-8配置
- `src/main/resources/pump-config.json` - 内置配置文件

## 🎯 使用建议

### **推荐使用方式**
1. **双击运行**：`run-utf8.bat`
2. **命令行运行**：`.\run-utf8.bat`

### **如果仍有问题**
1. **检查控制台**：确保使用支持UTF-8的终端
2. **检查字体**：使用支持中文的字体（如微软雅黑）
3. **使用PowerShell**：PowerShell通常有更好的UTF-8支持

## 🔍 故障排除

### **问题1：仍然显示乱码**
**解决方案**：
```bash
# 手动设置编码
chcp 65001
java -Dfile.encoding=UTF-8 -Dconsole.encoding=UTF-8 -Duser.timezone=Asia/Shanghai -jar pump30.jar
```

### **问题2：启动脚本无法运行**
**解决方案**：
```bash
# 直接运行Java命令
java -Dfile.encoding=UTF-8 -jar pump30.jar
```

### **问题3：PowerShell执行策略限制**
**解决方案**：
```powershell
# 临时允许脚本执行
Set-ExecutionPolicy -ExecutionPolicy Bypass -Scope Process
```

## 📊 测试验证

### **正确显示示例**
```
===============================================
           PUMP30 价格监控系统
===============================================

设置UTF-8编码...

启动PUMP30价格监控系统...
按 Ctrl+C 停止程序

执行价格监控任务 #1
PUMP价格监控系统已启动，监控间隔: 200ms
报警配置 - 启用: true, 买入阈值: $30.00, 卖出阈值: $30.00, 音频类型: CUSTOM

2025-07-15 14:26:45.461 : ===============PUMP监控 ===============
GATE单价: $6042.00
池买入100W个PUMP: $6055.54，差价：$-13.54，做升
池卖出100W个PUMP: $6049.16，差价：$7.16，做跌
```

### **错误显示示例**
```
PUMP浠锋牸鐩戞帶绯荤粺宸插惎鍔紝鐩戞帶闂撮殧: 200ms
鎶ヨ閰嶇疆 - 鍚敤: true, 涔板叆闃堝€? $30.00
```

## 💡 最佳实践

1. **始终使用UTF-8脚本**：避免编码问题
2. **设置合适的字体**：确保中文显示正常
3. **使用现代终端**：Windows Terminal等有更好的UTF-8支持
4. **避免路径中的中文**：减少编码相关问题

## 🎉 总结

通过设置正确的编码参数，PUMP30现在可以完美显示中文：
- ✅ **中文界面**：所有提示信息正确显示
- ✅ **价格信息**：监控数据清晰可读
- ✅ **报警信息**：报警提示正常显示
- ✅ **时间格式**：时间戳正确显示

**使用 `run-utf8.bat` 启动脚本即可享受完美的中文显示体验！** 🎊
