# PUMP价格监控系统文档中心

## 📚 文档导航

### 🎯 核心技术文档 (新增)
- **[技术文档总览](README-TECHNICAL.md)** - 完整技术文档导航和概述
- **[系统架构文档](architecture.md)** - 系统整体架构、组件设计、技术栈详解
- **[实现逻辑详解](implementation-logic.md)** - 核心业务逻辑、算法实现、技术细节
- **[测试验证策略](testing-strategy.md)** - 测试计划、验证方案、质量保证体系

### 📖 项目基础文档
- [项目概述](PROJECT-STATUS.md) - 项目当前状态和功能概述
- [MVP说明](README-MVP.md) - 最小可行产品功能说明
- [快速启动指南](../快速启动指南.md) - 系统安装和启动指南

### 🔧 技术配置文档
- [完整环境配置](complete-env-setup.md) - 开发环境完整配置指南
- [Maven安装指南](install-maven.md) - Maven构建工具安装
- [JAR构建指南](jar-build-guide.md) - 项目打包和部署指南

### ⚙️ 系统配置文档
- [JSON配置使用说明](JSON配置使用说明.md) - 配置文件详细说明
- [JAR包部署配置说明](JAR包部署配置说明.md) - 生产环境部署配置
- [中文显示修复说明](中文显示修复说明.md) - 中文字符显示问题解决方案

### 🛠️ 问题修复文档
- [PUMP价格监控系统优化修复报告](PUMP价格监控系统优化修复报告.md)
- [PUMP价格计算问题修复报告](PUMP价格计算问题修复报告.md)
- [Jupiter Quote API修复报告](Jupiter_Quote_API_修复报告.md)
- [买入卖出逻辑修复报告](买入卖出逻辑修复报告.md)
- [API限制和报警优化说明](API限制和报警优化说明.md)

### 🧪 测试验证文档
- [验证报告](verification-report.md) - 系统功能验证报告
- [报警音测试说明](报警音测试说明.md) - 音频告警功能测试
- [自定义音频使用指南](自定义音频使用指南.md) - 自定义音频文件配置

---

## 🎯 快速导航指南

### 👨‍💻 开发人员
1. **架构了解**: [系统架构文档](architecture.md) → 理解系统整体设计
2. **实现细节**: [实现逻辑详解](implementation-logic.md) → 掌握核心业务逻辑
3. **开发环境**: [完整环境配置](complete-env-setup.md) → 搭建开发环境
4. **测试验证**: [测试验证策略](testing-strategy.md) → 了解测试体系

### 🏗️ 架构师/技术负责人
1. **技术总览**: [技术文档总览](README-TECHNICAL.md) → 全面了解技术栈
2. **系统架构**: [系统架构文档](architecture.md) → 深入理解架构设计
3. **扩展规划**: 查看架构文档中的扩展性设计章节
4. **性能监控**: 参考架构文档中的性能指标与监控章节

### 🧪 测试工程师/QA
1. **测试策略**: [测试验证策略](testing-strategy.md) → 了解完整测试体系
2. **测试工具**: 查看`tools/`目录下的测试工具集
3. **验证报告**: [验证报告](verification-report.md) → 了解当前测试状态
4. **问题排查**: 参考各种修复报告文档

### 🚀 运维人员
1. **部署指南**: [JAR包部署配置说明](JAR包部署配置说明.md)
2. **配置管理**: [JSON配置使用说明](JSON配置使用说明.md)
3. **监控告警**: 参考架构文档中的运维监控章节
4. **故障处理**: 查看相关修复报告和实现逻辑文档

### 👥 新用户入门
1. **项目了解**: [项目概述](PROJECT-STATUS.md) → 了解系统功能
2. **快速体验**: [快速启动指南](../快速启动指南.md) → 快速运行系统
3. **功能说明**: [MVP说明](README-MVP.md) → 了解核心功能
4. **技术概览**: [技术文档总览](README-TECHNICAL.md) → 技术架构概述

---

## 📊 文档特色

### 🎨 可视化设计
- **Mermaid流程图**: 系统架构、数据流向、交互流程
- **表格对比**: 配置参数、性能指标、测试结果
- **代码片段**: 关键实现逻辑的代码引用

### 🔍 技术深度
- **架构设计**: 完整的系统架构和组件设计
- **实现细节**: 核心算法和业务逻辑详解
- **测试体系**: 全面的测试策略和验证方案

### 📈 实用性强
- **配置指南**: 详细的配置说明和最佳实践
- **故障排除**: 常见问题和解决方案
- **扩展指导**: 系统扩展和功能增强指南

---

## 🔄 文档更新记录

| 日期 | 更新内容 | 影响文档 |
|------|----------|----------|
| 2025-01-16 | 新增完整技术文档体系 | 新增4个核心技术文档 |
| 2025-01-15 | 系统功能优化和修复 | 更新多个修复报告 |
| 2025-01-14 | MVP功能完成 | 更新项目状态文档 |

---

## 📞 技术支持

### 🛠️ 开发调试
- **测试工具**: 使用`tools/`目录下的专用测试程序
- **日志分析**: 参考实现逻辑文档中的日志管理章节
- **性能调优**: 查看架构文档中的性能优化方案

### 🔧 配置问题
- **编码问题**: [中文显示修复说明](中文显示修复说明.md)
- **音频问题**: [自定义音频使用指南](自定义音频使用指南.md)
- **API问题**: [API限制和报警优化说明](API限制和报警优化说明.md)

### 📋 问题反馈
- **功能问题**: 参考相关修复报告文档
- **性能问题**: 查看测试验证策略文档
- **配置问题**: 参考配置相关文档

---

## 📊 项目状态

- **当前版本**: MVP 1.0 (技术文档完整版)
- **开发状态**: ✅ 核心功能完成，✅ 技术文档完整
- **测试状态**: ✅ 基础功能测试通过，✅ 测试体系建立
- **部署状态**: ✅ 支持JAR包独立部署
- **文档状态**: ✅ 完整技术文档体系建立

---

## 🎯 文档使用建议

### 📖 阅读顺序建议
1. **初次接触**: README-TECHNICAL.md → architecture.md
2. **深入开发**: implementation-logic.md → testing-strategy.md
3. **问题排查**: 相关修复报告 → 配置文档
4. **系统维护**: 架构文档运维章节 → 测试验证策略

### 🔍 快速查找
- **系统架构问题** → [系统架构文档](architecture.md)
- **业务逻辑问题** → [实现逻辑详解](implementation-logic.md)
- **测试相关问题** → [测试验证策略](testing-strategy.md)
- **配置相关问题** → 系统配置文档分类

---

**文档维护**: AI Agent  
**最后更新**: 2025-01-16  
**文档版本**: v1.0 (完整技术文档版)
