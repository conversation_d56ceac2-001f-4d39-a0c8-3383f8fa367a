# PowerShell 环境变量配置测试脚本
# 此脚本避免了BAT脚本的字符编码问题

# 设置控制台编码为UTF-8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

Write-Host "========================================"
Write-Host "环境变量配置测试 (PowerShell版本)"
Write-Host "========================================"
Write-Host ""

# 测试JAVA_HOME
Write-Host "测试JAVA_HOME..."
if ($env:JAVA_HOME) {
    Write-Host "JAVA_HOME: $env:JAVA_HOME"
} else {
    Write-Host "JAVA_HOME 未设置"
}
Write-Host ""

# 测试MAVEN_HOME
Write-Host "测试MAVEN_HOME..."
if ($env:MAVEN_HOME) {
    Write-Host "MAVEN_HOME: $env:MAVEN_HOME"
} else {
    Write-Host "MAVEN_HOME 未设置"
}
Write-Host ""

# 测试Java命令
Write-Host "测试Java命令..."
try {
    $javaVersion = & java -version 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Java命令可用"
        Write-Host "Java版本:"
        $javaVersion | ForEach-Object { Write-Host "  $_" }
    } else {
        Write-Host "❌ Java命令失败"
    }
} catch {
    Write-Host "❌ Java命令失败: $($_.Exception.Message)"
}
Write-Host ""

# 测试Maven命令
Write-Host "测试Maven命令..."
try {
    $mavenVersion = & mvn -version 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Maven命令可用"
        Write-Host "Maven版本:"
        $mavenVersion | ForEach-Object { Write-Host "  $_" }
    } else {
        Write-Host "❌ Maven命令失败"
    }
} catch {
    Write-Host "❌ Maven命令失败: $($_.Exception.Message)"
}
Write-Host ""

# 测试PATH环境变量
Write-Host "测试PATH环境变量..."
$pathEntries = $env:PATH -split ";"
$javaInPath = $pathEntries | Where-Object { $_ -like "*java*" }
$mavenInPath = $pathEntries | Where-Object { $_ -like "*maven*" }

if ($javaInPath) {
    Write-Host "✅ Java相关路径在PATH中:"
    $javaInPath | ForEach-Object { Write-Host "  $_" }
} else {
    Write-Host "❌ Java相关路径不在PATH中"
}

if ($mavenInPath) {
    Write-Host "✅ Maven相关路径在PATH中:"
    $mavenInPath | ForEach-Object { Write-Host "  $_" }
} else {
    Write-Host "❌ Maven相关路径不在PATH中"
}

Write-Host ""
Write-Host "========================================"
Write-Host "环境变量测试完成"
Write-Host "========================================"

# 如果不在IDE中运行，暂停等待用户确认
if (-not $env:TERM_PROGRAM) {
    Write-Host ""
    Write-Host "按任意键继续..."
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
} 