# PUMP价格监控系统 - 技术架构文档

## 系统概述

PUMP价格监控与套利分析系统是一个基于Spring Boot 2.1.1的实时数字货币监控平台，采用微服务架构设计，支持高并发、低延迟的价格数据处理和分析，专门监控CEX（Gate.io）和DEX（Jupiter）之间的PUMP价格差异。

## 技术栈

### 后端技术栈
- **框架**: Spring Boot 2.1.1
- **语言**: Java 8+
- **构建工具**: Maven
- **容器化**: Docker
- **日志框架**: SLF4J + Logback
- **定时任务**: Spring Scheduler
- **HTTP客户端**: RestTemplate
- **数据缓存**: Redis（可选）

### 前端技术栈
- **框架**: 基于Spring Boot内置的Web服务
- **模板引擎**: Thymeleaf（可选）
- **静态资源**: HTML5 + CSS3 + JavaScript
- **UI组件**: Bootstrap（可选）

### 运行环境
- **应用服务器**: Embedded Tomcat 9.0.13
- **端口配置**: 5072 (HTTP)
- **JVM要求**: OpenJDK 8或更高版本
- **内存要求**: 最小256MB，推荐512MB
- **网络要求**: 稳定的外网连接

## 系统架构

### 整体架构图
```
┌─────────────────────────────────────────────────────────────────┐
│                        PUMP价格监控系统                          │
├─────────────────────────────────────────────────────────────────┤
│                        Web界面层                                │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │   实时价格面板   │  │   套利分析面板   │  │   系统状态面板   │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
├─────────────────────────────────────────────────────────────────┤
│                        应用服务层                                │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │   Controller    │  │   WebSocket     │  │   Error         │  │
│  │   (HTTP API)    │  │   (实时推送)     │  │   Handler       │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
├─────────────────────────────────────────────────────────────────┤
│                        业务逻辑层                                │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │   QuoteManager  │  │   PriceAnalyzer │  │   AlertManager  │  │
│  │   (价格管理)     │  │   (分析引擎)     │  │   (告警管理)     │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
├─────────────────────────────────────────────────────────────────┤
│                        数据服务层                                │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │ PumpPoolService │  │   CacheService  │  │   ConfigService │  │
│  │  (价格获取)      │  │   (缓存管理)     │  │   (配置管理)     │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
├─────────────────────────────────────────────────────────────────┤
│                        基础设施层                                │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │   Scheduler     │  │   Logging       │  │   Monitoring    │  │
│  │   (定时任务)     │  │   (日志记录)     │  │   (监控告警)     │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
├─────────────────────────────────────────────────────────────────┤
│                        外部依赖                                  │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │   Price APIs    │  │   Redis Cache   │  │   Monitoring    │  │
│  │   (价格数据源)   │  │   (可选缓存)     │  │   (外部监控)     │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
└─────────────────────────────────────────────────────────────────┘
```

## 核心组件设计

### 1. 价格数据获取服务 (PumpPoolService)
**职责**: 从Gate.io和Jupiter获取实时PUMP价格数据

**接口设计**:
```java
public interface PumpPoolService {
    PriceData getCurrentPrice() throws PriceServiceException;
    BigDecimal getBuyPrice(BigDecimal amount);
    BigDecimal getSellPrice(BigDecimal amount);
    boolean healthCheck();
}
```

**实现特点**:
- 支持多数据源切换
- 自动重试机制
- 缓存优化
- 错误恢复

### 2. 价格分析引擎 (QuoteManager)
**职责**: 价格差异计算和套利分析

**核心功能**:
- 实时价格差异计算
- 套利机会识别
- 交易建议生成
- 风险评估

**算法实现**:
```java
public class QuoteManager {
    
    /**
     * 计算套利机会
     * @param buyPrice 买入价格
     * @param sellPrice 卖出价格
     * @param amount 交易数量
     * @return ArbitrageOpportunity 套利机会
     */
    public ArbitrageOpportunity calculateArbitrage(
        BigDecimal buyPrice, 
        BigDecimal sellPrice, 
        BigDecimal amount
    ) {
        BigDecimal spread = sellPrice.subtract(buyPrice);
        BigDecimal profit = spread.multiply(amount);
        
        String recommendation = spread.compareTo(BigDecimal.ZERO) > 0 
            ? "做升" : "做跌";
            
        return new ArbitrageOpportunity(spread, profit, recommendation);
    }
}
```

### 3. 定时任务调度器 (Scheduler)
**职责**: 定时执行价格监控任务

**配置**:
```java
@Component
public class PriceMonitorScheduler {
    
    @Autowired
    private QuoteManager quoteManager;
    
    @Scheduled(fixedDelay = 2000) // 每2秒执行一次
    public void monitorPrices() {
        try {
            quoteManager.updatePrices();
            logger.info("=============== TON监控 ===============");
            // 价格监控逻辑
        } catch (Exception e) {
            logger.error("价格监控异常", e);
        }
    }
}
```

## 数据流设计

### 1. 实时价格数据流
```
External API → TonPoolService → Cache → QuoteManager → Web Interface
     ↓              ↓              ↓           ↓            ↓
  价格源API      数据获取服务      缓存层      分析引擎      用户界面
```

### 2. 错误处理流程
```
API Call Failed → Retry Mechanism → Fallback API → Error Logging → User Alert
     ↓                 ↓                ↓              ↓           ↓
  API调用失败        重试机制          备用API        错误日志      用户告警
```

## 配置管理

### 应用配置 (application.yml)
```yaml
server:
  port: 5072
  
spring:
  application:
    name: ton-price-monitor
  
# 价格服务配置
price:
  api:
    primary:
      url: https://api.binance.com/api/v3/ticker/price?symbol=TONUSDT
      timeout: 5000
    fallback:
      url: https://api.okex.com/api/v5/market/ticker?instId=TON-USDT
      timeout: 5000
  
  cache:
    ttl: 2000
    enabled: true
    
  monitor:
    interval: 2000
    amount: 1000
    
# 日志配置
logging:
  level:
    com.bitcoin.alarm: INFO
    org.springframework.scheduling: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} %5p %5t --- [%15.15t] %-40.40c{1.} : %m%n"
```

## 部署架构

### Docker容器化部署
```dockerfile
FROM openjdk:8-jre-alpine

WORKDIR /app

COPY ton20.jar app.jar

EXPOSE 5072

ENTRYPOINT ["java", "-jar", "app.jar"]
```

### 部署配置
```yaml
# docker-compose.yml
version: '3.8'

services:
  ton-monitor:
    build: .
    ports:
      - "5072:5072"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - JAVA_OPTS=-Xmx512m -Xms256m
    restart: unless-stopped
    
  redis:
    image: redis:6-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    
volumes:
  redis_data:
```

## 监控和运维

### 1. 应用监控
- **健康检查**: `/actuator/health`
- **性能指标**: `/actuator/metrics`
- **日志监控**: 基于SLF4J的结构化日志

### 2. 关键指标
- **API响应时间**: 平均<1秒
- **价格更新频率**: 每2秒
- **系统可用性**: >99%
- **错误率**: <5%

### 3. 告警配置
```java
@Component
public class AlertManager {
    
    @EventListener
    public void handlePriceServiceError(PriceServiceErrorEvent event) {
        if (event.getConsecutiveErrors() > 3) {
            // 发送告警通知
            sendAlert("价格服务连续失败", event.getException());
        }
    }
}
```

## 扩展性设计

### 1. 水平扩展
- 支持多实例部署
- 负载均衡配置
- 状态无关设计

### 2. 功能扩展
- 新增价格源接入
- 多币种支持
- 更多分析指标

### 3. 性能优化
- 连接池配置
- 缓存策略优化
- 异步处理

## 安全考虑

### 1. 数据安全
- API密钥加密存储
- 敏感信息脱敏
- 访问日志记录

### 2. 网络安全
- HTTPS通信
- 防火墙配置
- 访问控制

### 3. 应用安全
- 输入验证
- 异常处理
- 资源限制

---

**版本**: 1.0  
**创建日期**: 2025-01-15  
**最后更新**: 2025-01-15  
**架构师**: AI Agent  
**审核状态**: 待审核

---

**相关文档**:
- [PBI-001产品需求文档](../delivery/PBI-001/prd.md)
- [系统部署指南](./deployment.md)
- [API接口文档](./api.md) 