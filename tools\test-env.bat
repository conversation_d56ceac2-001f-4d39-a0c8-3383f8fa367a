@echo off
chcp 65001 >nul
echo ========================================
echo 环境变量配置测试
echo ========================================
echo.

echo 测试JAVA_HOME...
if defined JAVA_HOME (
    echo JAVA_HOME: %JAVA_HOME%
) else (
    echo JAVA_HOME 未设置
)

echo.
echo 测试MAVEN_HOME...
if defined MAVEN_HOME (
    echo MAVEN_HOME: %MAVEN_HOME%
) else (
    echo MAVEN_HOME 未设置
)

echo.
echo 测试Java命令...
java -version
if %errorlevel% neq 0 (
    echo Java命令失败
    exit /b 1
)

echo.
echo 测试Maven命令...
mvn -version
if %errorlevel% neq 0 (
    echo Maven命令失败
    exit /b 1
)

echo.
echo ========================================
echo 环境配置测试完成！
echo ========================================
echo.

echo 如果上面所有测试都通过，现在可以运行：
echo .\quick-build-run.bat
echo.

pause 