import java.awt.Toolkit;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 测试报警音系统
 * 验证差价计算逻辑和报警音触发条件
 * 
 * <AUTHOR> Agent
 * @version 1.0
 * @since 2025-01-15
 */
public class TestAlertSoundSystem {
    
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
    
    public static void main(String[] args) {
        System.out.println("=== PUMP价格监控报警音系统测试 ===");
        System.out.println();
        
        // 测试差价计算逻辑
        testPriceDifferenceLogic();
        
        System.out.println();
        
        // 测试报警音触发条件
        testAlertTriggerConditions();
        
        System.out.println();
        
        // 测试系统提示音
        testSystemBeep();
    }
    
    private static void testPriceDifferenceLogic() {
        System.out.println("=== 1. 差价计算逻辑测试 ===");
        
        // 模拟价格数据
        BigDecimal gateTotalPrice = new BigDecimal("5634.00");
        BigDecimal jupiterBuyPrice = new BigDecimal("5641.95");
        BigDecimal jupiterSellPrice = new BigDecimal("5635.63");
        
        // 计算差价
        BigDecimal buyDifference = jupiterBuyPrice.subtract(gateTotalPrice);
        BigDecimal sellDifference = jupiterSellPrice.subtract(gateTotalPrice);
        
        System.out.println("价格数据:");
        System.out.printf("  Gate.io总价(100万PUMP): $%.2f%n", gateTotalPrice);
        System.out.printf("  Ultra API买入总价: $%.2f%n", jupiterBuyPrice);
        System.out.printf("  Ultra API卖出总价: $%.2f%n", jupiterSellPrice);
        System.out.println();
        
        System.out.println("差价计算:");
        System.out.printf("  买入差价 = Ultra买入价 - Gate价格 = $%.2f - $%.2f = $%.2f%n", 
            jupiterBuyPrice, gateTotalPrice, buyDifference);
        System.out.printf("  卖出差价 = Ultra卖出价 - Gate价格 = $%.2f - $%.2f = $%.2f%n", 
            jupiterSellPrice, gateTotalPrice, sellDifference);
        System.out.println();
        
        System.out.println("差价含义:");
        System.out.printf("  买入差价 $%.2f %s (Ultra API买入成本%s)%n", 
            buyDifference, 
            buyDifference.compareTo(BigDecimal.ZERO) > 0 ? "> 0" : "< 0",
            buyDifference.compareTo(BigDecimal.ZERO) > 0 ? "更高" : "更低");
        System.out.printf("  卖出差价 $%.2f %s (Ultra API卖出收入%s)%n", 
            sellDifference, 
            sellDifference.compareTo(BigDecimal.ZERO) > 0 ? "> 0" : "< 0",
            sellDifference.compareTo(BigDecimal.ZERO) > 0 ? "更高" : "更低");
    }
    
    private static void testAlertTriggerConditions() {
        System.out.println("=== 2. 报警触发条件测试 ===");
        
        BigDecimal buyThreshold = new BigDecimal("10.00");
        BigDecimal sellThreshold = new BigDecimal("10.00");
        
        System.out.println("报警配置:");
        System.out.printf("  买入报警阈值: $%.2f%n", buyThreshold);
        System.out.printf("  卖出报警阈值: $%.2f%n", sellThreshold);
        System.out.printf("  冷却时间: 30秒%n");
        System.out.println();
        
        // 测试不同的差价场景
        testScenario("正常差价", new BigDecimal("7.95"), new BigDecimal("1.63"), buyThreshold, sellThreshold);
        testScenario("买入套利机会", new BigDecimal("-15.50"), new BigDecimal("5.20"), buyThreshold, sellThreshold);
        testScenario("卖出套利机会", new BigDecimal("8.30"), new BigDecimal("12.80"), buyThreshold, sellThreshold);
        testScenario("双向套利机会", new BigDecimal("-20.00"), new BigDecimal("25.00"), buyThreshold, sellThreshold);
    }
    
    private static void testScenario(String scenarioName, BigDecimal buyDiff, BigDecimal sellDiff, 
                                   BigDecimal buyThreshold, BigDecimal sellThreshold) {
        System.out.println("场景: " + scenarioName);
        System.out.printf("  买入差价: $%.2f", buyDiff);
        
        // 买入报警条件：差价为负且绝对值超过阈值
        boolean buyAlert = buyDiff.compareTo(BigDecimal.ZERO) < 0 && 
                          buyDiff.abs().compareTo(buyThreshold) >= 0;
        System.out.printf(" -> %s%n", buyAlert ? "🔥 触发买入报警" : "无报警");
        
        System.out.printf("  卖出差价: $%.2f", sellDiff);
        
        // 卖出报警条件：差价为正且超过阈值
        boolean sellAlert = sellDiff.compareTo(BigDecimal.ZERO) > 0 && 
                           sellDiff.compareTo(sellThreshold) >= 0;
        System.out.printf(" -> %s%n", sellAlert ? "🔥 触发卖出报警" : "无报警");
        System.out.println();
    }
    
    private static void testSystemBeep() {
        System.out.println("=== 3. 系统提示音测试 ===");
        System.out.println("即将播放测试提示音...");
        
        try {
            Thread.sleep(1000);
            
            System.out.println("播放买入报警音 (2声):");
            playTestBeep(2);
            
            Thread.sleep(1000);
            
            System.out.println("播放卖出报警音 (3声):");
            playTestBeep(3);
            
            System.out.println("提示音测试完成！");
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
    
    private static void playTestBeep(int count) {
        try {
            Toolkit toolkit = Toolkit.getDefaultToolkit();
            for (int i = 0; i < count; i++) {
                toolkit.beep();
                if (i < count - 1) {
                    Thread.sleep(200);
                }
            }
        } catch (Exception e) {
            System.out.println("播放提示音失败: " + e.getMessage());
        }
    }
    
    static {
        System.out.println("=== 报警音系统配置说明 ===");
        System.out.println();
        System.out.println("配置参数 (application.properties):");
        System.out.println("  pump.alert.enabled=true              # 启用报警音");
        System.out.println("  pump.alert.buy-threshold=10.00       # 买入报警阈值($)");
        System.out.println("  pump.alert.sell-threshold=10.00      # 卖出报警阈值($)");
        System.out.println("  pump.alert.cooldown=30000            # 冷却时间(ms)");
        System.out.println("  pump.alert.sound-type=SYSTEM         # 音频类型");
        System.out.println();
        System.out.println("报警触发条件:");
        System.out.println("  买入报警: 买入差价 < 0 且 |差价| >= 阈值 (Ultra买入成本更低)");
        System.out.println("  卖出报警: 卖出差价 > 0 且 差价 >= 阈值 (Ultra卖出收入更高)");
        System.out.println();
        System.out.println("音频类型:");
        System.out.println("  SYSTEM: 系统提示音 (买入2声, 卖出3声)");
        System.out.println("  MULTIPLE: 多重提示音 (买入4声, 卖出5声)");
        System.out.println("  CUSTOM: 自定义音频文件 (待扩展)");
        System.out.println();
    }
}
