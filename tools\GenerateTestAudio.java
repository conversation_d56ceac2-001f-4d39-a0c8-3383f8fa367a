import javax.sound.sampled.*;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;

/**
 * 生成测试音频文件
 * 创建简单的提示音WAV文件用于测试
 * 
 * <AUTHOR> Agent
 * @version 1.0
 * @since 2025-01-15
 */
public class GenerateTestAudio {
    
    public static void main(String[] args) {
        System.out.println("=== 生成测试音频文件 ===");
        System.out.println();
        
        try {
            // 创建sounds目录
            File soundsDir = new File("sounds");
            if (!soundsDir.exists()) {
                soundsDir.mkdirs();
                System.out.println("创建目录: " + soundsDir.getAbsolutePath());
            }
            
            // 生成买入报警音 (较低频率，2声)
            generateBeepSound("sounds/up.wav", 800, 200, 2);
            System.out.println("✅ 生成买入报警音: sounds/up.wav");

            // 生成卖出报警音 (较高频率，3声)
            generateBeepSound("sounds/down.wav", 1200, 150, 3);
            System.out.println("✅ 生成卖出报警音: sounds/down.wav");
            
            System.out.println();
            System.out.println("音频文件生成完成！");
            System.out.println("请将配置改为: pump.alert.sound-type=CUSTOM");
            
        } catch (Exception e) {
            System.err.println("生成音频文件失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 生成提示音WAV文件
     * 
     * @param filename 文件名
     * @param frequency 频率 (Hz)
     * @param duration 每声持续时间 (ms)
     * @param beepCount 提示音次数
     */
    private static void generateBeepSound(String filename, int frequency, int duration, int beepCount) 
            throws IOException, LineUnavailableException {
        
        float sampleRate = 44100;
        int sampleSizeInBits = 16;
        int channels = 1;
        boolean signed = true;
        boolean bigEndian = false;
        
        AudioFormat format = new AudioFormat(sampleRate, sampleSizeInBits, channels, signed, bigEndian);
        
        // 计算总时长 (包括间隔)
        int totalDurationMs = beepCount * duration + (beepCount - 1) * 100; // 100ms间隔
        int totalSamples = (int) (sampleRate * totalDurationMs / 1000);
        
        byte[] audioData = new byte[totalSamples * 2]; // 16位 = 2字节
        
        int currentSample = 0;
        
        for (int beep = 0; beep < beepCount; beep++) {
            // 生成提示音
            int beepSamples = (int) (sampleRate * duration / 1000);
            for (int i = 0; i < beepSamples; i++) {
                double angle = 2.0 * Math.PI * i * frequency / sampleRate;
                short sample = (short) (Short.MAX_VALUE * Math.sin(angle) * 0.5); // 50%音量
                
                if (currentSample * 2 + 1 < audioData.length) {
                    audioData[currentSample * 2] = (byte) (sample & 0xFF);
                    audioData[currentSample * 2 + 1] = (byte) ((sample >> 8) & 0xFF);
                }
                currentSample++;
            }
            
            // 添加间隔 (除了最后一声)
            if (beep < beepCount - 1) {
                int silenceSamples = (int) (sampleRate * 100 / 1000); // 100ms静音
                for (int i = 0; i < silenceSamples && currentSample < totalSamples; i++) {
                    if (currentSample * 2 + 1 < audioData.length) {
                        audioData[currentSample * 2] = 0;
                        audioData[currentSample * 2 + 1] = 0;
                    }
                    currentSample++;
                }
            }
        }
        
        // 写入WAV文件
        ByteArrayInputStream bais = new ByteArrayInputStream(audioData);
        AudioInputStream audioInputStream = new AudioInputStream(bais, format, totalSamples);
        
        File outputFile = new File(filename);
        AudioSystem.write(audioInputStream, AudioFileFormat.Type.WAVE, outputFile);
        
        audioInputStream.close();
        bais.close();
    }
    
    static {
        System.out.println("=== 自定义音频配置说明 ===");
        System.out.println();
        System.out.println("📁 音频文件放置位置:");
        System.out.println("  方式1: src/main/resources/sounds/");
        System.out.println("    ├── up.wav     (买入报警音)");
        System.out.println("    └── down.wav   (卖出报警音)");
        System.out.println();
        System.out.println("  方式2: ./sounds/");
        System.out.println("    ├── up.wav     (买入报警音)");
        System.out.println("    └── down.wav   (卖出报警音)");
        System.out.println();
        System.out.println("🎵 音频文件要求:");
        System.out.println("  - 格式: WAV");
        System.out.println("  - 编码: PCM 16位");
        System.out.println("  - 采样率: 44.1kHz");
        System.out.println("  - 文件大小: < 1MB");
        System.out.println("  - 播放时长: 1-3秒");
        System.out.println();
        System.out.println("⚙️ 启用方法:");
        System.out.println("  在 application.properties 中设置:");
        System.out.println("  pump.alert.sound-type=CUSTOM");
        System.out.println();
    }
}
