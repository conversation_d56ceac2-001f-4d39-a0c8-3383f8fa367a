import java.math.BigDecimal;
import java.util.Scanner;
import java.awt.Toolkit;
import java.io.File;
import java.io.InputStream;
import java.util.concurrent.CompletableFuture;
import javax.sound.sampled.*;

/**
 * 报警音测试工具
 * 用于测试买入和卖出报警音的播放功能
 * 
 * <AUTHOR> Agent
 * @version 1.0
 * @since 2025-01-15
 */
public class TestAlertSound {
    
    public static void main(String[] args) {
        System.out.println("=== PUMP报警音测试工具 ===");
        System.out.println();
        
        // 模拟配置服务
        TestConfigService configService = new TestConfigService();
        
        // 创建报警服务实例
        TestAlertSoundService alertService = new TestAlertSoundService(configService);
        
        Scanner scanner = new Scanner(System.in);
        
        while (true) {
            showMenu();
            String choice = scanner.nextLine().trim();
            
            switch (choice) {
                case "1":
                    testBuyAlert(alertService);
                    break;
                case "2":
                    testSellAlert(alertService);
                    break;
                case "3":
                    testSystemSound(alertService);
                    break;
                case "4":
                    testCustomSound(alertService);
                    break;
                case "5":
                    testMultipleSound(alertService);
                    break;
                case "6":
                    showCurrentConfig(configService);
                    break;
                case "7":
                    changeConfig(configService, scanner);
                    break;
                case "0":
                    System.out.println("退出测试工具");
                    return;
                default:
                    System.out.println("无效选择，请重新输入");
            }
            
            System.out.println();
        }
    }
    
    private static void showMenu() {
        System.out.println("请选择测试项目：");
        System.out.println("1. 测试买入报警音 (up.wav)");
        System.out.println("2. 测试卖出报警音 (down.wav)");
        System.out.println("3. 测试系统音效 (SYSTEM)");
        System.out.println("4. 测试自定义音频 (CUSTOM)");
        System.out.println("5. 测试多重音效 (MULTIPLE)");
        System.out.println("6. 查看当前配置");
        System.out.println("7. 修改配置");
        System.out.println("0. 退出");
        System.out.print("请输入选择 (0-7): ");
    }
    
    private static void testBuyAlert(TestAlertSoundService alertService) {
        System.out.println("🔥 测试买入报警音...");
        System.out.println("模拟场景：Gate.io价格更高，Ultra API买入更便宜");
        
        BigDecimal gatePrice = new BigDecimal("5620.00");
        BigDecimal ultraBuyPrice = new BigDecimal("5595.00");
        BigDecimal buyDifference = gatePrice.subtract(ultraBuyPrice); // $25差价
        
        System.out.printf("Gate.io总价: $%.2f%n", gatePrice);
        System.out.printf("Ultra买入总价: $%.2f%n", ultraBuyPrice);
        System.out.printf("买入差价: $%.2f%n", buyDifference);
        
        alertService.checkBuyAlert(buyDifference, ultraBuyPrice, gatePrice);
        
        System.out.println("✅ 买入报警音测试完成");
    }
    
    private static void testSellAlert(TestAlertSoundService alertService) {
        System.out.println("🔥 测试卖出报警音...");
        System.out.println("模拟场景：Ultra API卖出收入更高");
        
        BigDecimal gatePrice = new BigDecimal("5605.00");
        BigDecimal ultraSellPrice = new BigDecimal("5630.00");
        BigDecimal sellDifference = ultraSellPrice.subtract(gatePrice); // $25差价
        
        System.out.printf("Gate.io总价: $%.2f%n", gatePrice);
        System.out.printf("Ultra卖出总价: $%.2f%n", ultraSellPrice);
        System.out.printf("卖出差价: $%.2f%n", sellDifference);
        
        alertService.checkSellAlert(sellDifference, ultraSellPrice, gatePrice);
        
        System.out.println("✅ 卖出报警音测试完成");
    }
    
    private static void testSystemSound(TestAlertSoundService alertService) {
        System.out.println("🔊 测试系统音效...");
        alertService.getConfigService().setSoundType("SYSTEM");
        
        System.out.println("播放买入系统音效 (2声)...");
        alertService.playTestSound(TestAlertSoundService.AlertType.BUY_OPPORTUNITY);
        
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        System.out.println("播放卖出系统音效 (3声)...");
        alertService.playTestSound(TestAlertSoundService.AlertType.SELL_OPPORTUNITY);
        
        System.out.println("✅ 系统音效测试完成");
    }
    
    private static void testCustomSound(TestAlertSoundService alertService) {
        System.out.println("🎵 测试自定义音频...");
        alertService.getConfigService().setSoundType("CUSTOM");
        
        System.out.println("播放自定义买入音频 (up.wav)...");
        alertService.playTestSound(TestAlertSoundService.AlertType.BUY_OPPORTUNITY);
        
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        System.out.println("播放自定义卖出音频 (down.wav)...");
        alertService.playTestSound(TestAlertSoundService.AlertType.SELL_OPPORTUNITY);
        
        System.out.println("✅ 自定义音频测试完成");
    }
    
    private static void testMultipleSound(TestAlertSoundService alertService) {
        System.out.println("🔊 测试多重音效...");
        alertService.getConfigService().setSoundType("MULTIPLE");
        
        System.out.println("播放多重买入音效 (4声)...");
        alertService.playTestSound(TestAlertSoundService.AlertType.BUY_OPPORTUNITY);
        
        try {
            Thread.sleep(3000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        System.out.println("播放多重卖出音效 (5声)...");
        alertService.playTestSound(TestAlertSoundService.AlertType.SELL_OPPORTUNITY);
        
        System.out.println("✅ 多重音效测试完成");
    }
    
    private static void showCurrentConfig(TestConfigService configService) {
        System.out.println("📋 当前配置：");
        System.out.printf("报警启用: %s%n", configService.isAlertEnabled());
        System.out.printf("买入阈值: $%.2f%n", configService.getBuyThreshold());
        System.out.printf("卖出阈值: $%.2f%n", configService.getSellThreshold());
        System.out.printf("音频类型: %s%n", configService.getSoundType());
    }
    
    private static void changeConfig(TestConfigService configService, Scanner scanner) {
        System.out.println("🔧 修改配置：");
        System.out.println("1. 修改买入阈值");
        System.out.println("2. 修改卖出阈值");
        System.out.println("3. 修改音频类型");
        System.out.println("4. 启用/禁用报警");
        System.out.print("请选择 (1-4): ");
        
        String choice = scanner.nextLine().trim();
        
        switch (choice) {
            case "1":
                System.out.print("输入新的买入阈值: $");
                try {
                    double threshold = Double.parseDouble(scanner.nextLine().trim());
                    configService.setBuyThreshold(new BigDecimal(threshold));
                    System.out.println("✅ 买入阈值已更新");
                } catch (NumberFormatException e) {
                    System.out.println("❌ 无效数字格式");
                }
                break;
            case "2":
                System.out.print("输入新的卖出阈值: $");
                try {
                    double threshold = Double.parseDouble(scanner.nextLine().trim());
                    configService.setSellThreshold(new BigDecimal(threshold));
                    System.out.println("✅ 卖出阈值已更新");
                } catch (NumberFormatException e) {
                    System.out.println("❌ 无效数字格式");
                }
                break;
            case "3":
                System.out.println("选择音频类型：");
                System.out.println("1. SYSTEM (系统音效)");
                System.out.println("2. CUSTOM (自定义音频)");
                System.out.println("3. MULTIPLE (多重音效)");
                System.out.print("请选择 (1-3): ");
                String soundChoice = scanner.nextLine().trim();
                switch (soundChoice) {
                    case "1":
                        configService.setSoundType("SYSTEM");
                        System.out.println("✅ 音频类型已设置为 SYSTEM");
                        break;
                    case "2":
                        configService.setSoundType("CUSTOM");
                        System.out.println("✅ 音频类型已设置为 CUSTOM");
                        break;
                    case "3":
                        configService.setSoundType("MULTIPLE");
                        System.out.println("✅ 音频类型已设置为 MULTIPLE");
                        break;
                    default:
                        System.out.println("❌ 无效选择");
                }
                break;
            case "4":
                boolean enabled = !configService.isAlertEnabled();
                configService.setAlertEnabled(enabled);
                System.out.printf("✅ 报警已%s%n", enabled ? "启用" : "禁用");
                break;
            default:
                System.out.println("❌ 无效选择");
        }
    }
}

/**
 * 测试用配置服务
 */
class TestConfigService {
    private boolean alertEnabled = true;
    private BigDecimal buyThreshold = new BigDecimal("20.00");
    private BigDecimal sellThreshold = new BigDecimal("20.00");
    private String soundType = "CUSTOM";

    public boolean isAlertEnabled() { return alertEnabled; }
    public void setAlertEnabled(boolean enabled) { this.alertEnabled = enabled; }

    public BigDecimal getBuyThreshold() { return buyThreshold; }
    public void setBuyThreshold(BigDecimal threshold) { this.buyThreshold = threshold; }

    public BigDecimal getSellThreshold() { return sellThreshold; }
    public void setSellThreshold(BigDecimal threshold) { this.sellThreshold = threshold; }

    public String getSoundType() { return soundType; }
    public void setSoundType(String type) { this.soundType = type; }
}

/**
 * 测试用报警服务
 */
class TestAlertSoundService {
    private final TestConfigService configService;
    private volatile boolean isBuyAudioPlaying = false;
    private volatile boolean isSellAudioPlaying = false;

    public TestAlertSoundService(TestConfigService configService) {
        this.configService = configService;
    }

    public TestConfigService getConfigService() {
        return configService;
    }

    public void checkBuyAlert(BigDecimal buyDifference, BigDecimal buyPrice, BigDecimal gatePrice) {
        if (!configService.isAlertEnabled() || buyDifference == null) {
            System.out.println("⚠️ 报警已禁用或差价为空");
            return;
        }

        if (shouldTriggerBuyAlert(buyDifference)) {
            String alertMessage = String.format("🔥 买入套利机会！Ultra API成本更低 $%.2f (Gate: $%.2f vs Ultra: $%.2f)",
                buyDifference, gatePrice, buyPrice);
            System.out.println(alertMessage);
            triggerAlert(AlertType.BUY_OPPORTUNITY, alertMessage);
        } else {
            System.out.printf("ℹ️ 买入差价 $%.2f 未超过阈值 $%.2f，不触发报警%n",
                buyDifference, configService.getBuyThreshold());
        }
    }

    public void checkSellAlert(BigDecimal sellDifference, BigDecimal sellPrice, BigDecimal gatePrice) {
        if (!configService.isAlertEnabled() || sellDifference == null) {
            System.out.println("⚠️ 报警已禁用或差价为空");
            return;
        }

        if (shouldTriggerSellAlert(sellDifference)) {
            String alertMessage = String.format("🔥 卖出套利机会！Ultra API收入更高 $%.2f (Ultra: $%.2f vs Gate: $%.2f)",
                sellDifference, sellPrice, gatePrice);
            System.out.println(alertMessage);
            triggerAlert(AlertType.SELL_OPPORTUNITY, alertMessage);
        } else {
            System.out.printf("ℹ️ 卖出差价 $%.2f 未超过阈值 $%.2f，不触发报警%n",
                sellDifference, configService.getSellThreshold());
        }
    }

    private boolean shouldTriggerBuyAlert(BigDecimal buyDifference) {
        boolean thresholdMet = buyDifference.compareTo(BigDecimal.ZERO) > 0 &&
                              buyDifference.compareTo(configService.getBuyThreshold()) > 0;
        boolean audioNotPlaying = !isBuyAudioPlaying;
        return thresholdMet && audioNotPlaying;
    }

    private boolean shouldTriggerSellAlert(BigDecimal sellDifference) {
        boolean thresholdMet = sellDifference.compareTo(BigDecimal.ZERO) > 0 &&
                              sellDifference.compareTo(configService.getSellThreshold()) > 0;
        boolean audioNotPlaying = !isSellAudioPlaying;
        return thresholdMet && audioNotPlaying;
    }

    public void playTestSound(AlertType alertType) {
        triggerAlert(alertType, "测试音频播放");
    }

    private void triggerAlert(AlertType alertType, String message) {
        CompletableFuture.runAsync(() -> {
            try {
                if (alertType == AlertType.BUY_OPPORTUNITY) {
                    isBuyAudioPlaying = true;
                } else {
                    isSellAudioPlaying = true;
                }

                String soundType = configService.getSoundType();
                System.out.printf("🔊 播放%s音效 (%s)...%n",
                    alertType == AlertType.BUY_OPPORTUNITY ? "买入" : "卖出", soundType);

                switch (soundType.toUpperCase()) {
                    case "SYSTEM":
                        playSystemBeep(alertType);
                        break;
                    case "CUSTOM":
                        playCustomSound(alertType);
                        break;
                    case "MULTIPLE":
                        playMultipleBeeps(alertType);
                        break;
                    default:
                        playSystemBeep(alertType);
                }

            } catch (Exception e) {
                System.err.println("播放报警音失败: " + e.getMessage());
            } finally {
                if (alertType == AlertType.BUY_OPPORTUNITY) {
                    isBuyAudioPlaying = false;
                } else {
                    isSellAudioPlaying = false;
                }
            }
        });
    }

    private void playSystemBeep(AlertType alertType) {
        try {
            Toolkit toolkit = Toolkit.getDefaultToolkit();
            int beepCount = alertType == AlertType.BUY_OPPORTUNITY ? 2 : 3;

            for (int i = 0; i < beepCount; i++) {
                toolkit.beep();
                if (i < beepCount - 1) {
                    Thread.sleep(200);
                }
            }

        } catch (Exception e) {
            System.err.println("播放系统提示音失败: " + e.getMessage());
        }
    }

    private void playCustomSound(AlertType alertType) {
        try {
            String audioFileName = alertType == AlertType.BUY_OPPORTUNITY ? "up.wav" : "down.wav";

            // 尝试从sounds目录加载
            File audioFile = new File("sounds/" + audioFileName);
            if (audioFile.exists()) {
                playWavFile(audioFile);
                System.out.println("✅ 播放自定义音频: " + audioFile.getAbsolutePath());
            } else {
                // 尝试从resources加载
                InputStream audioStream = getClass().getClassLoader().getResourceAsStream("sounds/" + audioFileName);
                if (audioStream != null) {
                    playWavFile(audioStream);
                    System.out.println("✅ 播放资源音频: sounds/" + audioFileName);
                } else {
                    System.out.println("⚠️ 未找到自定义音频文件: " + audioFileName + "，使用系统提示音");
                    playSystemBeep(alertType);
                }
            }

        } catch (Exception e) {
            System.err.println("播放自定义音频失败: " + e.getMessage() + "，使用系统提示音");
            playSystemBeep(alertType);
        }
    }

    private void playMultipleBeeps(AlertType alertType) {
        try {
            Toolkit toolkit = Toolkit.getDefaultToolkit();
            int beepCount = alertType == AlertType.BUY_OPPORTUNITY ? 4 : 5;

            for (int i = 0; i < beepCount; i++) {
                toolkit.beep();
                Thread.sleep(150);
            }

        } catch (Exception e) {
            System.err.println("播放多重提示音失败: " + e.getMessage());
        }
    }

    private void playWavFile(InputStream audioStream) throws Exception {
        try (AudioInputStream audioInputStream = AudioSystem.getAudioInputStream(audioStream)) {
            Clip clip = AudioSystem.getClip();
            clip.open(audioInputStream);
            clip.start();
            Thread.sleep(clip.getMicrosecondLength() / 1000);
            clip.close();
        }
    }

    private void playWavFile(File audioFile) throws Exception {
        try (AudioInputStream audioInputStream = AudioSystem.getAudioInputStream(audioFile)) {
            Clip clip = AudioSystem.getClip();
            clip.open(audioInputStream);
            clip.start();
            Thread.sleep(clip.getMicrosecondLength() / 1000);
            clip.close();
        }
    }

    public enum AlertType {
        BUY_OPPORTUNITY,
        SELL_OPPORTUNITY
    }
}
