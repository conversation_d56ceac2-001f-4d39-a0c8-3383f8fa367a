@echo off
echo ===============================================
echo           音频格式转换脚本
echo        M4A/MP3/MP4 转换为 WAV 格式
echo ===============================================
echo.

REM 检查FFmpeg是否安装
ffmpeg -version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到FFmpeg，请先安装FFmpeg
    echo 下载地址: https://ffmpeg.org/download.html
    echo 或使用Chocolatey安装: choco install ffmpeg
    pause
    exit /b 1
)

echo FFmpeg已安装，开始转换...
echo.

REM 创建输出目录
if not exist "sounds" mkdir sounds

REM 转换M4A文件
for %%f in (*.m4a) do (
    echo 转换: %%f
    ffmpeg -i "%%f" -ar 44100 -ac 2 -sample_fmt s16 "sounds\%%~nf.wav" -y
    if %errorlevel% equ 0 (
        echo ✅ 成功: sounds\%%~nf.wav
    ) else (
        echo ❌ 失败: %%f
    )
    echo.
)

REM 转换MP3文件
for %%f in (*.mp3) do (
    echo 转换: %%f
    ffmpeg -i "%%f" -ar 44100 -ac 2 -sample_fmt s16 "sounds\%%~nf.wav" -y
    if %errorlevel% equ 0 (
        echo ✅ 成功: sounds\%%~nf.wav
    ) else (
        echo ❌ 失败: %%f
    )
    echo.
)

REM 转换MP4文件（提取音频）
for %%f in (*.mp4) do (
    echo 转换: %%f (提取音频)
    ffmpeg -i "%%f" -vn -ar 44100 -ac 2 -sample_fmt s16 "sounds\%%~nf.wav" -y
    if %errorlevel% equ 0 (
        echo ✅ 成功: sounds\%%~nf.wav
    ) else (
        echo ❌ 失败: %%f
    )
    echo.
)

echo ===============================================
echo 转换完成！
echo 输出目录: sounds\
echo.
echo 请将以下文件重命名为报警音文件：
echo   up.wav    - 买入报警音
echo   down.wav  - 卖出报警音
echo ===============================================
pause
