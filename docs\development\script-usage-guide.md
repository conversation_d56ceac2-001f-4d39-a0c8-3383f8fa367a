# 脚本使用指南 - 解决中文字符乱码问题

## 问题解决方案

### 问题现象
之前运行BAT脚本时，中文字符显示为乱码：
```
鐜鍙橀噺閰嶇疆娴嬭瘯
娴嬭瘯JAVA_HOME...
```

### 解决方案
我们提供了两种解决方案：

#### 方案1：修复后的BAT脚本（推荐）
所有BAT脚本都已经修复，在开头添加了 `chcp 65001 >nul` 来设置UTF-8代码页。

#### 方案2：PowerShell脚本（最佳）
创建了PowerShell版本的脚本，完全避免编码问题。

## 可用脚本列表

### 修复后的BAT脚本（✅ 已修复乱码）

| 脚本名称 | 功能描述 | 使用方法 |
|---------|---------|---------|
| `test-env.bat` | 环境变量配置测试 | 双击运行或在命令行执行 |
| `maven-setup-guide.bat` | Maven环境变量配置指南 | 双击运行查看配置步骤 |
| `verification-script.bat` | 代码验证脚本 | 验证Jupiter API修复效果 |
| `quick-build-run.bat` | 一键打包运行 | Maven方式构建和运行 |
| `full-verification.bat` | 完整验证流程 | 全面测试系统功能 |
| `manual-build.bat` | 手动编译打包 | 无Maven的编译方式 |
| `run-jar.bat` | JAR运行脚本 | 运行已构建的JAR文件 |
| `run-mvp.bat` | MVP启动脚本 | 启动MVP版本系统 |
| `compile-check.bat` | 编译检查 | 检查编译环境和依赖 |
| `test-encoding-fix.bat` | 编码修复测试 | 验证字符编码修复效果 |

### PowerShell脚本（🌟 推荐使用）

| 脚本名称 | 功能描述 | 使用方法 |
|---------|---------|---------|
| `test-env.ps1` | 环境变量配置测试 | 右键选择"用PowerShell运行" |

## 使用建议

### 1. 优先使用PowerShell脚本
```powershell
# 在PowerShell中运行
.\test-env.ps1
```

### 2. 使用修复后的BAT脚本
```cmd
# 在命令行中运行
test-env.bat
```

### 3. 验证修复效果
```cmd
# 运行测试脚本验证修复效果
test-encoding-fix.bat
```

## 修复详情

### 技术原理
- **问题原因**: BAT文件使用UTF-8编码，但Windows命令行默认使用GBK编码
- **解决方案**: 在脚本开头添加 `chcp 65001 >nul` 设置UTF-8代码页
- **效果**: 中文字符正常显示

### 修复内容
每个BAT脚本都在开头添加了：
```batch
@echo off
chcp 65001 >nul
```

### 验证方法
运行 `test-encoding-fix.bat` 脚本，如果能看到正常的中文字符，说明修复成功。

## 故障排除

### 如果仍然有乱码问题

1. **检查系统区域设置**
   - 控制面板 → 区域 → 管理 → 更改系统区域设置
   - 确保使用UTF-8编码

2. **尝试PowerShell脚本**
   - PowerShell对Unicode支持更好
   - 使用 `.\test-env.ps1` 代替 `test-env.bat`

3. **检查控制台设置**
   - 右键点击命令行窗口标题栏
   - 选择"属性" → "选项"
   - 确保使用新版控制台

### 如果PowerShell脚本无法运行

1. **设置执行策略**
   ```powershell
   Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
   ```

2. **直接在PowerShell中运行**
   ```powershell
   powershell -ExecutionPolicy Bypass -File ".\test-env.ps1"
   ```

## 最佳实践

1. **开发环境**
   - 优先使用PowerShell脚本
   - 设置IDE编码为UTF-8
   - 配置Git处理行尾符

2. **生产环境**
   - 使用修复后的BAT脚本
   - 确保服务器支持UTF-8
   - 监控脚本执行日志

3. **团队协作**
   - 统一使用UTF-8编码
   - 文档化脚本使用方法
   - 定期验证脚本功能

## 相关文档

- [Windows BAT脚本编码问题解决方案](../technical/windows-bat-encoding-fix.md)
- [PowerShell脚本开发指南](../technical/powershell-guide.md)
- [环境配置指南](../technical/environment-setup.md)

---

**创建日期**: 2025-01-15
**更新日期**: 2025-01-15
**维护者**: AI Agent
**状态**: 已完成 