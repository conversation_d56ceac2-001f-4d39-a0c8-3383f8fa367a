@echo off
echo ===============================================
echo           PUMP Alert Sound Test
echo ===============================================
echo.

REM Check Java
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Java not found
    pause
    exit /b 1
)

echo Java OK
echo.

REM Compile
echo Compiling...
javac SimpleAlertTest.java
if %errorlevel% neq 0 (
    echo Compilation failed
    pause
    exit /b 1
)

echo Compilation OK
echo.

REM Check audio files
echo Checking audio files...
if exist "sounds\up.wav" (
    echo [OK] up.wav found
) else (
    echo [WARN] up.wav not found
)

if exist "sounds\down.wav" (
    echo [OK] down.wav found
) else (
    echo [WARN] down.wav not found
)

echo.
echo Running test...
echo.

REM Run test
java SimpleAlertTest

echo.
echo Test finished
pause
