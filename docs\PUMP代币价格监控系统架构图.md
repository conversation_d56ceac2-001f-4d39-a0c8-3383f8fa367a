graph TB
    subgraph "PUMP价格监控系统"
        A[PumpApplication<br/>主程序启动<br/>🚀] --> B[PriceMonitorScheduler<br/>定时调度器<br/>⏰]
        B --> C[PumpPriceService<br/>价格服务层<br/>💰]
        
        C --> D[GateIoApiClient<br/>CEX价格客户端<br/>🏦]
        C --> E[JupiterApiClientFixed<br/>DEX价格客户端<br/>🔄]
        C --> F[JupiterUltraApiClient<br/>Ultra API客户端<br/>⚡]
        
        B --> G[AlertSoundService<br/>音频告警服务<br/>🔔]
        
        H[PumpConfigService<br/>配置管理<br/>⚙️] --> B
        H --> G
        
        I[PriceCache<br/>价格缓存<br/>💾] --> E
        I --> F
        
        J[EncodingInitializer<br/>编码初始化<br/>🔤] --> A
    end
    
    subgraph "外部API服务"
        K[Gate.io API<br/>订单簿数据<br/>📊]
        L[Jupiter Quote API<br/>DEX报价<br/>💱]
        M[Jupiter Price API<br/>价格数据<br/>💲]
        N[Jupiter Ultra API<br/>增强路由<br/>🛣️]
    end
    
    subgraph "配置与资源"
        O[application.properties<br/>Spring配置<br/>🔧]
        P[pump-config.json<br/>业务配置<br/>📝]
        Q[up.wav / down.wav<br/>告警音频<br/>🎵]
    end
    
    D --> K
    E --> L
    E --> M
    F --> N
    
    H --> P
    A --> O
    G --> Q
    
    style A fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    style B fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    style C fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    style G fill:#fff3e0,stroke:#e65100,stroke-width:2px
    style H fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    style I fill:#f1f8e9,stroke:#33691e,stroke-width:2px