import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 测试100万个PUMP的价格计算
 * 验证输出是否为100万个PUMP的总价格
 * 
 * <AUTHOR> Agent
 * @version 1.0
 * @since 2025-01-15
 */
public class Test100WPumpPrice {
    
    public static void main(String[] args) {
        System.out.println("=== 测试100万个PUMP价格计算 ===");
        System.out.println();
        
        // 模拟100万个PUMP的价格计算
        testPumpPriceCalculation();
        
        System.out.println();
        System.out.println("=== 预期的实际输出格式 ===");
        System.out.println("现在启动Spring Boot应用程序应该每800ms输出：");
        System.out.println("Gate.io订单簿价格: 5771.69");
        System.out.println("Jupiter Quote API买入价格: 5877.56");
        System.out.println("Jupiter Quote API卖出价格: 5865.82");
        System.out.println();
        System.out.println("注意：这些都是100万个PUMP的总价格（USDT）");
    }
    
    /**
     * 测试100万个PUMP的价格计算
     */
    private static void testPumpPriceCalculation() {
        System.out.println("模拟100万个PUMP价格计算：");
        System.out.println();
        
        BigDecimal amount = new BigDecimal("1000000"); // 100万个PUMP
        
        // 模拟Gate.io单价
        BigDecimal gateSinglePrice = new BigDecimal("0.00577169");
        BigDecimal gateTotalPrice = gateSinglePrice.multiply(amount);
        
        // 模拟Jupiter Quote API返回的总价格（基于之前的测试结果）
        BigDecimal jupiterBuyTotalPrice = new BigDecimal("5877.56");
        BigDecimal jupiterSellTotalPrice = new BigDecimal("5865.82");
        
        System.out.println("计算过程：");
        System.out.println("Gate.io单价: " + gateSinglePrice + " USDT/PUMP");
        System.out.println("100万个PUMP数量: " + amount);
        System.out.println("Gate.io总价 = " + gateSinglePrice + " × " + amount + " = " + gateTotalPrice + " USDT");
        System.out.println();
        
        System.out.println("Jupiter Quote API直接返回总价格：");
        System.out.println("买入100万个PUMP需要: " + jupiterBuyTotalPrice + " USDT");
        System.out.println("卖出100万个PUMP获得: " + jupiterSellTotalPrice + " USDT");
        System.out.println();
        
        // 输出最终格式
        System.out.println("最终输出格式：");
        System.out.println("Gate.io订单簿价格: " + String.format("%.2f", gateTotalPrice));
        System.out.println("Jupiter Quote API买入价格: " + String.format("%.2f", jupiterBuyTotalPrice));
        System.out.println("Jupiter Quote API卖出价格: " + String.format("%.2f", jupiterSellTotalPrice));
        
        System.out.println();
        System.out.println("✅ 所有价格都是100万个PUMP的总价格");
        System.out.println("✅ 单位为USDT");
        System.out.println("✅ 保留2位小数");
        
        // 验证价格合理性
        BigDecimal expectedRange = new BigDecimal("6000"); // 预期约6000 USDT左右
        BigDecimal tolerance = new BigDecimal("1000"); // ±1000 USDT容忍度
        
        boolean gateReasonable = gateTotalPrice.subtract(expectedRange).abs().compareTo(tolerance) <= 0;
        boolean jupiterBuyReasonable = jupiterBuyTotalPrice.subtract(expectedRange).abs().compareTo(tolerance) <= 0;
        boolean jupiterSellReasonable = jupiterSellTotalPrice.subtract(expectedRange).abs().compareTo(tolerance) <= 0;
        
        System.out.println();
        System.out.println("价格合理性验证：");
        System.out.println("Gate.io价格合理性: " + (gateReasonable ? "✅ 通过" : "❌ 异常"));
        System.out.println("Jupiter买入价格合理性: " + (jupiterBuyReasonable ? "✅ 通过" : "❌ 异常"));
        System.out.println("Jupiter卖出价格合理性: " + (jupiterSellReasonable ? "✅ 通过" : "❌ 异常"));
    }
}
