# PUMP价格监控系统 - 启动故障排除指南

## 常见启动问题及解决方案

### 1. 端口被占用问题

**错误信息**: `Address already in use: bind`
**原因**: 端口8080被其他程序占用

**解决方案**:
1. **推荐方案**: 已禁用Web服务器
   - 修改`application.properties`添加：`spring.main.web-application-type=none`
   - 系统现在直接在控制台输出，不需要Web端口

2. **查找占用端口的进程**:
   ```bash
   netstat -ano | findstr :8080
   ```

3. **终止占用进程**:
   ```bash
   taskkill /F /PID <进程ID>
   ```

### 2. Java环境问题

**错误信息**: `java不是内部或外部命令`
**原因**: Java环境变量未正确配置

**解决方案**:
1. 运行`test-java-env.bat`验证Java环境
2. 手动设置环境变量：
   - `JAVA_HOME=F:\java-1.8.0`
   - `PATH`中添加`F:\java-1.8.0\bin`

### 3. Maven环境问题

**错误信息**: `mvn不是内部或外部命令`
**原因**: Maven环境变量未正确配置

**解决方案**:
1. 设置环境变量：
   - `MAVEN_HOME=F:\apache-maven-3.9.10`
   - `PATH`中添加`F:\apache-maven-3.9.10\bin`

### 4. 网络连接问题

**错误信息**: `Jupiter API连接超时`
**原因**: 网络连接问题或API地址错误

**解决方案**:
1. 检查网络连接
2. 验证PUMP代币地址是否正确
3. 增加API超时时间：
   ```properties
   jupiter.api.timeout=10000
   gate.api.timeout=10000
   ```

### 5. 编译错误

**错误信息**: `String.repeat(int)`不存在
**原因**: 使用了Java 11的方法但运行在Java 8上

**解决方案**:
- 已修复：使用自定义`repeatString()`方法替代

## 快速测试命令

### 验证环境
```bash
# 测试Java
java -version

# 测试Maven
mvn -version

# 测试网络连接
ping api.gateio.ws
ping api.jup.ag
```

### 启动系统
```bash
# 使用快速启动脚本
quick-start-test.bat

# 或者直接运行
mvn spring-boot:run
```

### 停止系统
- 在控制台按`Ctrl+C`

## 系统配置说明

### 当前配置
- **Web服务器**: 已禁用（控制台应用）
- **监控间隔**: 2秒
- **交易数量**: 100万个PUMP
- **超时设置**: 5秒

### 输出格式
```
==============================PUMP监控==============================
2025-01-15 14:30:25.123  : 现货价格100W个PUMP: 4000.00个USDT
2025-01-15 14:30:25.123  : 池买入100W个PUMP: 4020.00个USDT，差价：20.00，建议：在DEX买入
2025-01-15 14:30:25.123  : 池卖出100W个PUMP: 3980.00个USDT，差价：-20.00，建议：在CEX卖出
===============================================================
```

## 联系支持

如果问题仍然存在，请：
1. 运行`test-java-env.bat`获取环境信息
2. 保存错误日志信息
3. 检查网络连接状态 