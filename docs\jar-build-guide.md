# JAR打包和运行指南

## 🎯 目标
将PUMP价格监控系统打包成JAR文件，然后可以直接用`java -jar`命令运行。

## 📋 准备工作

### 1. 检查Java环境
```bash
java -version
```
需要Java 8或更高版本。

### 2. 选择打包方法

| 方法 | 优点 | 缺点 | 适用场景 |
|------|------|------|----------|
| **Maven打包** | 简单、标准、自动处理依赖 | 需要安装Maven | 推荐用于开发环境 |
| **手动打包** | 不需要额外工具 | 复杂、需要手动下载依赖 | 特殊环境或学习目的 |
| **IDE打包** | 图形界面、方便 | 需要IDE | 如果你用IntelliJ/Eclipse |

## 🚀 方法1: Maven打包（推荐）

### 步骤1: 安装Maven
参考 `install-maven.md` 文件安装Maven。

### 步骤2: 快速打包
```bash
# 一键打包运行
.\quick-build-run.bat

# 或者分步骤
.\build-jar.bat     # 仅打包
.\run-jar.bat       # 仅运行
```

### 步骤3: 手动运行
```bash
# 打包
mvn clean package -DskipTests

# 运行
java -jar target\pump-price-monitor-1.0-SNAPSHOT.jar
```

## 🛠️ 方法2: 手动打包（无Maven）

### 步骤1: 下载依赖
需要下载以下JAR文件到 `build\lib\` 目录：

```
spring-boot-starter-2.1.1.RELEASE.jar
spring-boot-starter-web-2.1.1.RELEASE.jar
jackson-databind-2.9.8.jar
httpclient-4.5.6.jar
spring-boot-2.1.1.RELEASE.jar
spring-core-5.1.3.RELEASE.jar
spring-context-5.1.3.RELEASE.jar
slf4j-api-1.7.25.jar
```

下载地址：https://repo1.maven.org/maven2/

### 步骤2: 运行打包脚本
```bash
.\manual-build.bat
```

### 步骤3: 运行JAR
```bash
java -cp "build\lib\*;build\pump-price-monitor-executable.jar" com.pump.PumpApplication
```

## 💻 方法3: IDE打包

### IntelliJ IDEA
1. 打开项目
2. Build → Build Artifacts → Build
3. 生成的JAR在 `out\artifacts\` 目录

### Eclipse
1. 右键项目 → Export
2. 选择 "Runnable JAR file"
3. 设置主类：`com.pump.PumpApplication`
4. 选择输出位置

## 🎮 运行JAR文件

### 基本运行
```bash
java -jar pump-price-monitor-1.0-SNAPSHOT.jar
```

### 带参数运行
```bash
# 指定配置文件
java -jar pump-price-monitor-1.0-SNAPSHOT.jar --spring.config.location=application.properties

# 指定日志级别
java -jar pump-price-monitor-1.0-SNAPSHOT.jar --logging.level.com.pump=DEBUG

# 指定监控间隔
java -jar pump-price-monitor-1.0-SNAPSHOT.jar --pump.monitor.interval=5000
```

### 后台运行
```bash
# Windows
start java -jar pump-price-monitor-1.0-SNAPSHOT.jar

# Linux/Mac
nohup java -jar pump-price-monitor-1.0-SNAPSHOT.jar &
```

## 🔧 常见问题

### 1. 找不到主类
确保MANIFEST.MF文件包含：
```
Main-Class: com.pump.PumpApplication
```

### 2. 依赖缺失
错误信息：`ClassNotFoundException`
解决：确保所有依赖JAR文件都在classpath中

### 3. 内存不足
添加JVM参数：
```bash
java -Xmx512m -jar pump-price-monitor-1.0-SNAPSHOT.jar
```

### 4. 端口占用
修改端口：
```bash
java -jar pump-price-monitor-1.0-SNAPSHOT.jar --server.port=8081
```

## 📊 验证运行效果

启动后应该看到类似输出：
```
2025-01-15 10:30:00.123 - Gate.io: 0.123456 vs Jupiter买入: 0.124000 vs Jupiter卖出: 0.122000
2025-01-15 10:30:02.456 - Gate.io: 0.123500 vs Jupiter买入: 0.124100 vs Jupiter卖出: 0.122100
```

关键验证点：
- ✅ 买入价格和卖出价格不同
- ✅ 每2秒更新一次
- ✅ 价格数据合理
- ✅ 无异常日志

## 📁 文件结构

```
pump/
├── target/                           # Maven构建输出
│   └── pump-price-monitor-1.0-SNAPSHOT.jar
├── build/                            # 手动构建输出
│   ├── lib/                          # 依赖JAR文件
│   └── pump-price-monitor-executable.jar
├── build-jar.bat                     # Maven打包脚本
├── run-jar.bat                       # JAR运行脚本
├── quick-build-run.bat               # 一键打包运行
├── manual-build.bat                  # 手动打包脚本
└── jar-build-guide.md                # 本指南
```

## 🎉 总结

**推荐流程**：
1. 安装Maven → 运行 `.\quick-build-run.bat`
2. 如果不想安装Maven → 使用IDE打包
3. 最后选择 → 手动打包

**优势**：
- 一次打包，处处运行
- 不需要源代码即可运行
- 部署简单
- 可以设置系统服务

打包成功后，只需要JAR文件和Java环境就能运行PUMP价格监控系统！ 