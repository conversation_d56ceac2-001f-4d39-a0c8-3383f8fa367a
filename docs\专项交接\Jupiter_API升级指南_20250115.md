# Jupiter API升级指南

**创建日期：** 2025年1月15日  
**适用版本：** PUMP价格监控系统 v1.0+  
**更新状态：** 最新  

---

## 1. 概述

本指南详细说明如何将Jupiter API从免费版本升级到付费版本，以及如何集成Swap API来获得更丰富的交易数据。

### 1.1 版本对比

| 特性 | 免费版本 | 付费版本 |
|------|---------|----------|
| 基础URL | `https://lite-api.jup.ag/` | `https://api.jup.ag/` |
| API Key | 不需要 | 需要 |
| 价格数据 | 支持 | 支持 |
| 交易报价 | 支持 | 支持 |
| 服务质量 | 基础 | 优化 |
| 速率限制 | 较严格 | 较宽松 |

---

## 2. 升级到付费版本

### 2.1 获取API Key

1. **注册账户**
   - 访问 [Jupiter官网](https://docs.jup.ag/)
   - 注册开发者账户
   - 申请API key

2. **API Key配置**
   - 登录开发者控制台
   - 生成新的API key
   - 记录API key（注意保密）

### 2.2 配置修改

#### 2.2.1 更新 `application.properties`

```properties
# Jupiter API配置 - 付费版本
jupiter.api.base-url=https://api.jup.ag/price/v3
jupiter.api.quote-url=https://api.jup.ag/swap/v1/quote
jupiter.api.key=your-actual-api-key-here
jupiter.api.timeout=30000
jupiter.api.connect-timeout=15000
jupiter.api.read-timeout=20000
```

#### 2.2.2 更新 `JupiterApiClient.java`

在类中添加API key支持：

```java
@Value("${jupiter.api.key:}")
private String apiKey;

// 在createRestTemplate方法中添加header
private RestTemplate createRestTemplate() {
    RestTemplate restTemplate = new RestTemplate();
    
    // 如果配置了API key，添加到header
    if (apiKey != null && !apiKey.isEmpty()) {
        restTemplate.getInterceptors().add((request, body, execution) -> {
            request.getHeaders().add("x-api-key", apiKey);
            return execution.execute(request, body);
        });
    }
    
    // ... 其他配置
    return restTemplate;
}
```

### 2.3 验证升级

1. **配置检查**
   ```bash
   # 运行系统检查
   java -jar target/pump-price-monitor.jar --check-config
   ```

2. **API连接测试**
   ```bash
   # 测试API连接
   curl -H "x-api-key: your-api-key" \
        "https://api.jup.ag/price/v3?ids=83cXjb9Z8iJSBK2DBacte1utUsygdotMBQVhJoSppump"
   ```

---

## 3. 集成Swap API

### 3.1 Swap API功能介绍

Swap API提供实际的交易报价，比Price API更适合需要成交预测的场景。

#### 3.1.1 主要功能
- 实时交易报价
- 买入/卖出价格计算
- 流动性分析
- 交易路径优化

#### 3.1.2 API端点
```
GET /swap/v1/quote?inputMint={token}&outputMint={token}&amount={amount}
```

### 3.2 集成步骤

#### 3.2.1 添加配置

在 `application.properties` 中添加：

```properties
# Swap API配置
jupiter.swap.enabled=true
jupiter.swap.default-amount=1000000
jupiter.swap.input-mint=83cXjb9Z8iJSBK2DBacte1utUsygdotMBQVhJoSppump
jupiter.swap.output-mint=EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v
```

#### 3.2.2 创建Swap客户端

```java
@Component
public class JupiterSwapClient {
    
    @Value("${jupiter.api.quote-url}")
    private String quoteUrl;
    
    public SwapQuoteResult getQuote(String inputMint, String outputMint, long amount) {
        String url = String.format("%s?inputMint=%s&outputMint=%s&amount=%d",
                quoteUrl, inputMint, outputMint, amount);
        
        // 实现API调用逻辑
        // 返回报价结果
    }
}
```

#### 3.2.3 数据模型

```java
public class SwapQuoteResult {
    private String inputMint;
    private String outputMint;
    private long inAmount;
    private long outAmount;
    private double priceImpact;
    private List<RouteStep> routePlan;
    
    // 计算实际汇率
    public double getExchangeRate() {
        return (double) outAmount / inAmount;
    }
}
```

### 3.3 使用示例

```java
// 获取1单位PUMP能换取的USDT数量
SwapQuoteResult quote = swapClient.getQuote(
    "83cXjb9Z8iJSBK2DBacte1utUsygdotMBQVhJoSppump", // PUMP
    "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v", // USDT
    1000000 // 1 PUMP (6 decimals)
);

System.out.println("1 PUMP = " + quote.getExchangeRate() + " USDT");
```

---

## 4. 性能优化建议

### 4.1 缓存策略
- 实现价格数据缓存
- 设置合理的TTL
- 使用Redis或内存缓存

### 4.2 请求优化
- 批量请求多个token
- 使用连接池
- 实现请求去重

### 4.3 错误处理
- 实现熔断机制
- 添加重试逻辑
- 监控API健康状态

---

## 5. 监控与告警

### 5.1 关键指标
- API响应时间
- 请求成功率
- 错误率统计
- 配额使用情况

### 5.2 告警规则
- API异常时发送告警
- 配额接近限制时提醒
- 性能指标异常时通知

---

## 6. 故障排除

### 6.1 常见问题

1. **API Key无效**
   - 检查key是否正确
   - 确认key权限设置
   - 检查header格式

2. **配额超限**
   - 检查使用量统计
   - 优化请求频率
   - 考虑升级套餐

3. **网络连接问题**
   - 检查代理配置
   - 验证网络可达性
   - 调整超时设置

### 6.2 调试技巧
- 启用详细日志
- 使用API测试工具
- 监控网络请求

---

## 7. 最佳实践

### 7.1 安全性
- 保护API key安全
- 使用环境变量存储敏感信息
- 定期轮换API key

### 7.2 可靠性
- 实现优雅降级
- 设置合理的超时
- 准备备用方案

### 7.3 可维护性
- 模块化设计
- 完善的文档
- 充分的测试覆盖

---

## 8. 总结

通过本指南，您可以：
1. 成功升级到Jupiter API付费版本
2. 集成Swap API获得更丰富的交易数据
3. 优化系统性能和可靠性
4. 建立完善的监控体系

建议根据实际业务需求选择合适的升级方案，并在升级过程中进行充分的测试。

---

**文档维护：** AI Agent  
**最后更新：** 2025年1月15日  
**版本：** v1.0 