import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.Proxy;
import java.net.InetSocketAddress;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 测试修复后的Jupiter Quote API实现
 * 验证100万个PUMP的价格是否在合理范围内（约$5800 USDT）
 * 
 * <AUTHOR> Agent
 * @version 1.0
 * @since 2025-01-15
 */
public class TestJupiterQuoteFix {
    
    // PUMP代币地址（用户指定的正确地址）
    private static final String PUMP_TOKEN = "pumpCmXqMfrsAkQ5r49WcJnRayYRqmXz6ae8H7H9Dfn";
    
    // USDT代币地址（基于截图验证）
    private static final String USDT_TOKEN = "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB";
    
    public static void main(String[] args) {
        System.out.println("=== 测试修复后的Jupiter Quote API ===");
        System.out.println("目标：验证100万个PUMP的价格约为$5800 USDT");
        System.out.println();
        
        // 测试100万个PUMP的报价
        BigDecimal pumpAmount = new BigDecimal("1000000");
        testQuotePrice(pumpAmount);
        
        System.out.println("\n=== 测试完成 ===");
    }
    
    /**
     * 测试指定数量PUMP的报价价格
     */
    private static void testQuotePrice(BigDecimal pumpAmount) {
        try {
            System.out.println("测试数量: " + pumpAmount + " PUMP");
            
            // 根据修复后的逻辑：统一使用PUMP->USDT查询
            String inputMint = PUMP_TOKEN;
            String outputMint = USDT_TOKEN;
            
            // 转换为最小单位（6位精度）
            BigDecimal amountInSmallestUnit = pumpAmount.multiply(new BigDecimal("1000000"));
            
            // 构建Quote API URL
            String quoteUrl = "https://lite-api.jup.ag/swap/v1/quote";
            String url = String.format("%s?inputMint=%s&outputMint=%s&amount=%s&slippageBps=50",
                                     quoteUrl, inputMint, outputMint, amountInSmallestUnit.toBigInteger());
            
            System.out.println("请求URL: " + url);
            System.out.println("查询参数:");
            System.out.println("  - inputMint: " + inputMint + " (PUMP)");
            System.out.println("  - outputMint: " + outputMint + " (USDT)");
            System.out.println("  - amount: " + amountInSmallestUnit.toBigInteger() + " (最小单位)");
            System.out.println();
            
            // 发送HTTP请求（使用代理）
            URL urlObj = new URL(url);

            // 配置SOCKS代理
            Proxy proxy = new Proxy(Proxy.Type.SOCKS, new InetSocketAddress("127.0.0.1", 7890));
            HttpURLConnection connection = (HttpURLConnection) urlObj.openConnection(proxy);

            connection.setRequestMethod("GET");
            connection.setRequestProperty("User-Agent", "PUMP-Monitor/1.0");
            connection.setRequestProperty("Accept", "application/json");
            connection.setConnectTimeout(15000);
            connection.setReadTimeout(20000);
            
            System.out.println("正在发送请求...");
            int responseCode = connection.getResponseCode();
            System.out.println("响应代码: " + responseCode);
            
            if (responseCode == 200) {
                // 读取响应
                BufferedReader reader = new BufferedReader(
                    new InputStreamReader(connection.getInputStream()));
                StringBuilder response = new StringBuilder();
                String line;
                
                while ((line = reader.readLine()) != null) {
                    response.append(line);
                }
                reader.close();
                
                String responseBody = response.toString();
                System.out.println("响应内容: " + responseBody);
                System.out.println();
                
                // 解析响应
                parseAndValidateResponse(responseBody, pumpAmount);
                
            } else {
                System.out.println("❌ API请求失败，响应代码: " + responseCode);
                
                // 读取错误信息
                BufferedReader errorReader = new BufferedReader(
                    new InputStreamReader(connection.getErrorStream()));
                StringBuilder errorResponse = new StringBuilder();
                String errorLine;
                
                while ((errorLine = errorReader.readLine()) != null) {
                    errorResponse.append(errorLine);
                }
                errorReader.close();
                
                System.out.println("错误信息: " + errorResponse.toString());
            }
            
        } catch (Exception e) {
            System.out.println("❌ 测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 解析并验证API响应
     */
    private static void parseAndValidateResponse(String responseBody, BigDecimal pumpAmount) {
        try {
            // 简单的JSON解析
            String inAmountKey = "\"inAmount\":\"";
            String outAmountKey = "\"outAmount\":\"";
            
            int inStartIndex = responseBody.indexOf(inAmountKey);
            int outStartIndex = responseBody.indexOf(outAmountKey);
            
            if (inStartIndex == -1 || outStartIndex == -1) {
                System.out.println("❌ 响应缺少inAmount或outAmount字段");
                return;
            }
            
            // 解析inAmount (PUMP最小单位)
            inStartIndex += inAmountKey.length();
            int inEndIndex = responseBody.indexOf("\"", inStartIndex);
            String inAmountStr = responseBody.substring(inStartIndex, inEndIndex);
            BigDecimal inAmount = new BigDecimal(inAmountStr);
            
            // 解析outAmount (USDT最小单位)
            outStartIndex += outAmountKey.length();
            int outEndIndex = responseBody.indexOf("\"", outStartIndex);
            String outAmountStr = responseBody.substring(outStartIndex, outEndIndex);
            BigDecimal outAmount = new BigDecimal(outAmountStr);
            
            System.out.println("=== 响应数据解析 ===");
            System.out.println("inAmount (PUMP最小单位): " + inAmount);
            System.out.println("outAmount (USDT最小单位): " + outAmount);
            
            // 转换为正常单位
            BigDecimal pumpNormal = inAmount.divide(new BigDecimal("1000000"), 8, RoundingMode.HALF_UP);
            BigDecimal usdtNormal = outAmount.divide(new BigDecimal("1000000"), 8, RoundingMode.HALF_UP);
            
            System.out.println("PUMP正常单位: " + pumpNormal);
            System.out.println("USDT正常单位: " + usdtNormal);
            
            // 计算单价
            BigDecimal unitPrice = usdtNormal.divide(pumpNormal, 8, RoundingMode.HALF_UP);
            System.out.println("单价: " + unitPrice + " USDT/PUMP");
            
            // 计算总价
            BigDecimal totalCost = unitPrice.multiply(pumpAmount);
            System.out.println("总价: " + totalCost + " USDT");
            System.out.println();
            
            // 验证价格合理性
            validatePriceReasonable(unitPrice, totalCost, pumpAmount);
            
        } catch (Exception e) {
            System.out.println("❌ 响应解析失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 验证价格合理性
     */
    private static void validatePriceReasonable(BigDecimal unitPrice, BigDecimal totalCost, BigDecimal pumpAmount) {
        System.out.println("=== 价格合理性验证 ===");

        // 基于正确PUMP代币的实际API测试：1 PUMP ≈ 0.0058 USDT
        BigDecimal expectedUnitPrice = new BigDecimal("0.0058");
        BigDecimal unitTolerance = new BigDecimal("0.002"); // ±0.002的容忍度

        boolean unitPriceValid = unitPrice.subtract(expectedUnitPrice).abs().compareTo(unitTolerance) <= 0;
        System.out.println("单价验证: " + unitPrice + " USDT/PUMP (预期: " + expectedUnitPrice + " ±" + unitTolerance + ") " +
                          (unitPriceValid ? "✅ 通过" : "⚠️ 偏离预期"));

        // 检查100万个PUMP的总价
        if (pumpAmount.compareTo(new BigDecimal("1000000")) == 0) {
            BigDecimal expectedTotal = new BigDecimal("5800"); // 基于正确PUMP代币约5800 USDT
            BigDecimal totalTolerance = new BigDecimal("1000"); // ±1000 USDT容忍度

            boolean totalPriceValid = totalCost.subtract(expectedTotal).abs().compareTo(totalTolerance) <= 0;
            System.out.println("总价验证: " + totalCost + " USDT (预期: " + expectedTotal + " ±" + totalTolerance + ") " +
                              (totalPriceValid ? "✅ 通过" : "❌ 异常"));

            // 基本合理性检查
            boolean basicValid = unitPrice.compareTo(BigDecimal.ZERO) > 0 && totalCost.compareTo(BigDecimal.ZERO) > 0;

            if (basicValid && totalPriceValid) {
                System.out.println("\n🎉 修复成功！Jupiter Quote API正常工作");
                System.out.println("   - API响应正确：能够解析inAmount和outAmount");
                System.out.println("   - 单位转换正确：正确处理6位精度");
                System.out.println("   - 价格计算正确：单价 = outAmount / inAmount");
                System.out.println("   - 当前PUMP价格：约" + unitPrice + " USDT/PUMP");
                System.out.println("   - 100万个PUMP成本：约" + totalCost + " USDT");
                System.out.println("   - 价格符合预期：与截图显示的约0.0058 USDT/PUMP接近");
            } else {
                System.out.println("\n⚠️ 需要进一步检查API响应或计算逻辑");
            }
        }
    }
}
