import java.math.BigDecimal;

/**
 * 测试当前的买入卖出逻辑
 * 验证调用流程是否正确
 * 
 * <AUTHOR> Agent
 * @version 1.0
 * @since 2025-01-15
 */
public class TestCurrentLogic {
    
    public static void main(String[] args) {
        System.out.println("=== 测试当前买入卖出逻辑 ===");
        System.out.println();
        
        BigDecimal amount = new BigDecimal("1000000"); // 100万个PUMP
        
        // 模拟当前的逻辑
        System.out.println("1. 调用 getBuyPrice(amount, \"DEX\"):");
        System.out.println("   -> 调用 getQuotePrice(amount, true)");
        System.out.println("   -> isBuy = true");
        simulateGetQuotePrice(amount, true);
        
        System.out.println();
        
        System.out.println("2. 调用 getSellPrice(amount, \"DEX\"):");
        System.out.println("   -> 调用 getQuotePrice(amount, false)");
        System.out.println("   -> isBuy = false");
        simulateGetQuotePrice(amount, false);
        
        System.out.println();
        System.out.println("=== 根据截图分析 ===");
        System.out.println("截图1 (卖出操作): inputMint=USDT, outputMint=PUMP, amount=6000000000");
        System.out.println("  -> 这是买入PUMP的参数！");
        System.out.println("截图2 (买入操作): inputMint=PUMP, outputMint=USDT, amount=1000000000000");
        System.out.println("  -> 这是卖出PUMP的参数！");
        System.out.println();
        System.out.println("结论: 买入和卖出的逻辑完全颠倒了！");
    }
    
    private static void simulateGetQuotePrice(BigDecimal amount, boolean isBuy) {
        String inputMint, outputMint;
        BigDecimal queryAmount;
        
        if (isBuy) {
            // 买入PUMP：用USDT买PUMP
            inputMint = "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB"; // USDT
            outputMint = "pumpCmXqMfrsAkQ5r49WcJnRayYRqmXz6ae8H7H9Dfn"; // PUMP
            queryAmount = new BigDecimal("6000"); // 固定使用$6000
            System.out.println("   买入逻辑:");
            System.out.println("     inputMint: " + inputMint + " (USDT)");
            System.out.println("     outputMint: " + outputMint + " (PUMP)");
            System.out.println("     amount: " + queryAmount.multiply(new BigDecimal("1000000")) + " (6000 USDT最小单位)");
        } else {
            // 卖出PUMP：用PUMP换USDT
            inputMint = "pumpCmXqMfrsAkQ5r49WcJnRayYRqmXz6ae8H7H9Dfn"; // PUMP
            outputMint = "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB"; // USDT
            queryAmount = amount; // 使用100万个PUMP
            System.out.println("   卖出逻辑:");
            System.out.println("     inputMint: " + inputMint + " (PUMP)");
            System.out.println("     outputMint: " + outputMint + " (USDT)");
            System.out.println("     amount: " + queryAmount.multiply(new BigDecimal("1000000")) + " (100万PUMP最小单位)");
        }
    }
}
