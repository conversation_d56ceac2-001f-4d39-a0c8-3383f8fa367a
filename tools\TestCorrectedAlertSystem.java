import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 测试修正后的报警系统
 * 验证正确的差价计算逻辑和报警触发条件
 * 
 * <AUTHOR> Agent
 * @version 1.0
 * @since 2025-01-15
 */
public class TestCorrectedAlertSystem {
    
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
    
    public static void main(String[] args) {
        System.out.println("=== 修正后的PUMP价格监控报警系统测试 ===");
        System.out.println();
        
        // 测试修正后的差价计算逻辑
        testCorrectedPriceDifferenceLogic();
        
        System.out.println();
        
        // 测试修正后的报警触发条件
        testCorrectedAlertTriggerConditions();
        
        System.out.println();
        
        // 显示自定义音频配置说明
        showCustomAudioSetup();
    }
    
    private static void testCorrectedPriceDifferenceLogic() {
        System.out.println("=== 1. 修正后的差价计算逻辑 ===");
        
        // 模拟价格数据
        BigDecimal gateTotalPrice = new BigDecimal("5634.00");
        BigDecimal jupiterBuyPrice = new BigDecimal("5641.95");
        BigDecimal jupiterSellPrice = new BigDecimal("5635.63");
        
        // 修正后的差价计算
        BigDecimal buyDifference = gateTotalPrice.subtract(jupiterBuyPrice);   // Gate - Ultra买入
        BigDecimal sellDifference = jupiterSellPrice.subtract(gateTotalPrice); // Ultra卖出 - Gate
        
        System.out.println("价格数据:");
        System.out.printf("  Gate.io总价(100万PUMP): $%.2f%n", gateTotalPrice);
        System.out.printf("  Ultra API买入总价: $%.2f%n", jupiterBuyPrice);
        System.out.printf("  Ultra API卖出总价: $%.2f%n", jupiterSellPrice);
        System.out.println();
        
        System.out.println("✅ 修正后的差价计算:");
        System.out.printf("  买入差价 = Gate价格 - Ultra买入价 = $%.2f - $%.2f = $%.2f%n", 
            gateTotalPrice, jupiterBuyPrice, buyDifference);
        System.out.printf("  卖出差价 = Ultra卖出价 - Gate价格 = $%.2f - $%.2f = $%.2f%n", 
            jupiterSellPrice, gateTotalPrice, sellDifference);
        System.out.println();
        
        System.out.println("差价含义解释:");
        System.out.printf("  买入差价 $%.2f %s -> %s%n", 
            buyDifference, 
            buyDifference.compareTo(BigDecimal.ZERO) > 0 ? "> 0" : "< 0",
            buyDifference.compareTo(BigDecimal.ZERO) > 0 ? "Gate.io更贵，Ultra API买入更便宜 🔥" : "Ultra API买入更贵");
        System.out.printf("  卖出差价 $%.2f %s -> %s%n", 
            sellDifference, 
            sellDifference.compareTo(BigDecimal.ZERO) > 0 ? "> 0" : "< 0",
            sellDifference.compareTo(BigDecimal.ZERO) > 0 ? "Ultra API卖出收入更高 🔥" : "Gate.io卖出更有利");
    }
    
    private static void testCorrectedAlertTriggerConditions() {
        System.out.println("=== 2. 修正后的报警触发条件测试 ===");
        
        BigDecimal buyThreshold = new BigDecimal("30.00");  // 调整后的阈值
        BigDecimal sellThreshold = new BigDecimal("30.00"); // 调整后的阈值
        
        System.out.println("报警配置 (已调整):");
        System.out.printf("  买入报警阈值: $%.2f (+$20)%n", buyThreshold);
        System.out.printf("  卖出报警阈值: $%.2f (+$20)%n", sellThreshold);
        System.out.printf("  冷却时间: 30秒%n");
        System.out.println();
        
        System.out.println("✅ 修正后的报警触发条件:");
        System.out.println("  买入报警: 买入差价 > 0 且 差价 >= $30.00 (Gate.io更贵，Ultra API买入更便宜)");
        System.out.println("  卖出报警: 卖出差价 > 0 且 差价 >= $30.00 (Ultra API卖出收入更高)");
        System.out.println();
        
        // 测试不同的差价场景
        testCorrectedScenario("正常差价", new BigDecimal("-7.95"), new BigDecimal("1.63"), buyThreshold, sellThreshold);
        testCorrectedScenario("买入套利机会", new BigDecimal("35.50"), new BigDecimal("5.20"), buyThreshold, sellThreshold);
        testCorrectedScenario("卖出套利机会", new BigDecimal("-8.30"), new BigDecimal("32.80"), buyThreshold, sellThreshold);
        testCorrectedScenario("双向套利机会", new BigDecimal("40.00"), new BigDecimal("45.00"), buyThreshold, sellThreshold);
    }
    
    private static void testCorrectedScenario(String scenarioName, BigDecimal buyDiff, BigDecimal sellDiff, 
                                            BigDecimal buyThreshold, BigDecimal sellThreshold) {
        System.out.println("场景: " + scenarioName);
        System.out.printf("  买入差价: $%.2f", buyDiff);
        
        // 修正后的买入报警条件：差价为正且超过阈值
        boolean buyAlert = buyDiff.compareTo(BigDecimal.ZERO) > 0 && 
                          buyDiff.compareTo(buyThreshold) >= 0;
        System.out.printf(" -> %s%n", buyAlert ? "🔥 触发买入报警" : "无报警");
        
        System.out.printf("  卖出差价: $%.2f", sellDiff);
        
        // 卖出报警条件保持不变：差价为正且超过阈值
        boolean sellAlert = sellDiff.compareTo(BigDecimal.ZERO) > 0 && 
                           sellDiff.compareTo(sellThreshold) >= 0;
        System.out.printf(" -> %s%n", sellAlert ? "🔥 触发卖出报警" : "无报警");
        System.out.println();
    }
    
    private static void showCustomAudioSetup() {
        System.out.println("=== 3. 自定义音频配置说明 ===");
        System.out.println();
        
        System.out.println("✅ 支持的音频格式: WAV");
        System.out.println();
        
        System.out.println("📁 音频文件放置位置:");
        System.out.println("  方式1: 放在项目的 src/main/resources/sounds/ 目录下");
        System.out.println("    - buy_alert.wav  (买入报警音)");
        System.out.println("    - sell_alert.wav (卖出报警音)");
        System.out.println();
        System.out.println("  方式2: 放在运行目录的 sounds/ 文件夹下");
        System.out.println("    - ./sounds/buy_alert.wav");
        System.out.println("    - ./sounds/sell_alert.wav");
        System.out.println();
        
        System.out.println("⚙️ 配置方法:");
        System.out.println("  在 application.properties 中设置:");
        System.out.println("  pump.alert.sound-type=CUSTOM");
        System.out.println();
        
        System.out.println("🎵 音频类型选项:");
        System.out.println("  SYSTEM   - 系统提示音 (买入2声, 卖出3声)");
        System.out.println("  MULTIPLE - 多重提示音 (买入4声, 卖出5声)");
        System.out.println("  CUSTOM   - 自定义WAV文件 (buy_alert.wav, sell_alert.wav)");
        System.out.println();
        
        System.out.println("📝 音频文件建议:");
        System.out.println("  - 文件大小: 建议小于1MB");
        System.out.println("  - 播放时长: 建议1-3秒");
        System.out.println("  - 音频质量: 16位PCM WAV格式");
        System.out.println("  - 音量适中: 避免过于刺耳");
        System.out.println();
        
        System.out.println("🔧 故障处理:");
        System.out.println("  - 如果自定义音频文件不存在或播放失败");
        System.out.println("  - 系统会自动回退到系统提示音");
        System.out.println("  - 错误信息会记录在日志中");
        
        System.out.println();
        System.out.println("现在系统支持完整的自定义音频功能！");
    }
    
    static {
        System.out.println("=== 修正内容总结 ===");
        System.out.println();
        System.out.println("✅ 1. 差价计算逻辑修正:");
        System.out.println("     买入差价 = Gate.io总价 - Ultra API买入总价");
        System.out.println("     卖出差价 = Ultra API卖出总价 - Gate.io总价");
        System.out.println();
        System.out.println("✅ 2. 报警阈值调整:");
        System.out.println("     买入阈值: $10.00 -> $30.00 (+$20)");
        System.out.println("     卖出阈值: $10.00 -> $30.00 (+$20)");
        System.out.println();
        System.out.println("✅ 3. 自定义音频支持:");
        System.out.println("     支持WAV格式音频文件");
        System.out.println("     自动回退机制");
        System.out.println("     灵活的文件放置位置");
        System.out.println();
    }
}
