package com.pump.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.pump.config.PumpConfigService;

import java.awt.Toolkit;
import java.io.File;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.concurrent.CompletableFuture;
import javax.sound.sampled.*;

/**
 * 报警音服务（新版本）
 * 使用JSON配置文件和优化的冷却机制
 * 只在上一段音频播放完成后才播放新音频
 * 
 * <AUTHOR> Agent
 * @version 1.0
 * @since 2025-01-15
 */
@Service
public class AlertSoundService {

    private static final Logger logger = LoggerFactory.getLogger(AlertSoundService.class);
    
    @Autowired
    private PumpConfigService configService;
    
    // 音频播放状态控制（防止重叠播放）
    private volatile boolean isBuyAudioPlaying = false;
    private volatile boolean isSellAudioPlaying = false;
    
    /**
     * 检查并触发买入价格报警
     * 
     * @param buyDifference 买入差价
     * @param buyPrice 买入价格
     * @param gatePrice Gate.io价格
     */
    public void checkBuyAlert(BigDecimal buyDifference, BigDecimal buyPrice, BigDecimal gatePrice) {
        if (!configService.isAlertEnabled() || buyDifference == null) {
            return;
        }
        
        // 检查是否超过阈值且没有正在播放音频
        if (shouldTriggerBuyAlert(buyDifference)) {
            String alertMessage = String.format("🔥 买入套利机会！Ultra API成本更低 $%.2f (Gate: $%.2f vs Ultra: $%.2f)", 
                buyDifference, gatePrice, buyPrice);
            
            triggerAlert(AlertType.BUY_OPPORTUNITY, alertMessage);
        }
    }
    
    /**
     * 检查并触发卖出价格报警
     * 
     * @param sellDifference 卖出差价
     * @param sellPrice 卖出价格
     * @param gatePrice Gate.io价格
     */
    public void checkSellAlert(BigDecimal sellDifference, BigDecimal sellPrice, BigDecimal gatePrice) {
        if (!configService.isAlertEnabled() || sellDifference == null) {
            return;
        }
        
        // 检查是否超过阈值且没有正在播放音频
        if (shouldTriggerSellAlert(sellDifference)) {
            String alertMessage = String.format("🔥 卖出套利机会！Ultra API收入更高 $%.2f (Ultra: $%.2f vs Gate: $%.2f)", 
                sellDifference, sellPrice, gatePrice);
            
            triggerAlert(AlertType.SELL_OPPORTUNITY, alertMessage);
        }
    }
    
    /**
     * 检查是否应该触发买入报警
     * 买入报警触发条件：买入差价 = Gate.io总价 - Ultra API买入总价
     * 差价 > 阈值时触发，表示Gate.io更贵，Ultra API买入更便宜，有套利机会
     * 播放音频：up.wav
     */
    private boolean shouldTriggerBuyAlert(BigDecimal buyDifference) {
        // 买入差价 = Gate.io总价 - Ultra API买入总价，差价 > 阈值时触发
        boolean thresholdMet = buyDifference.compareTo(BigDecimal.ZERO) > 0 &&
                              buyDifference.compareTo(configService.getBuyThreshold()) > 0;

        // 如果启用连续播放，则忽略播放状态检查
        if (configService.isContinuousPlayEnabled()) {
            return thresholdMet;
        }

        // 检查是否有音频正在播放
        boolean audioNotPlaying = !isBuyAudioPlaying;

        return thresholdMet && audioNotPlaying;
    }
    
    /**
     * 检查是否应该触发卖出报警
     * 卖出报警触发条件：卖出差价 = Ultra API卖出总价 - Gate.io总价
     * 差价 > 阈值时触发，表示Ultra API卖出收入更高，有套利机会
     * 播放音频：down.wav
     */
    private boolean shouldTriggerSellAlert(BigDecimal sellDifference) {
        // 卖出差价 = Ultra API卖出总价 - Gate.io总价，差价 > 阈值时触发
        boolean thresholdMet = sellDifference.compareTo(BigDecimal.ZERO) > 0 &&
                              sellDifference.compareTo(configService.getSellThreshold()) > 0;

        // 如果启用连续播放，则忽略播放状态检查
        if (configService.isContinuousPlayEnabled()) {
            return thresholdMet;
        }

        // 检查是否有音频正在播放
        boolean audioNotPlaying = !isSellAudioPlaying;

        return thresholdMet && audioNotPlaying;
    }
    
    /**
     * 触发报警音
     * 
     * @param alertType 报警类型
     * @param message 报警消息
     */
    private void triggerAlert(AlertType alertType, String message) {
        // 异步播放报警音，避免阻塞主线程
        CompletableFuture.runAsync(() -> {
            try {
                // 设置播放状态
                if (alertType == AlertType.BUY_OPPORTUNITY) {
                    isBuyAudioPlaying = true;
                } else {
                    isSellAudioPlaying = true;
                }
                
                String soundType = configService.getSoundType();
                switch (soundType.toUpperCase()) {
                    case "SYSTEM":
                        playSystemBeep(alertType);
                        break;
                    case "CUSTOM":
                        playCustomSound(alertType);
                        break;
                    case "MULTIPLE":
                        playMultipleBeeps(alertType);
                        break;
                    default:
                        playSystemBeep(alertType);
                }
                
            } catch (Exception e) {
                logger.error("播放报警音失败: {}", e.getMessage());
            } finally {
                // 重置播放状态
                if (alertType == AlertType.BUY_OPPORTUNITY) {
                    isBuyAudioPlaying = false;
                } else {
                    isSellAudioPlaying = false;
                }
            }
        });
    }
    
    /**
     * 播放系统提示音
     */
    private void playSystemBeep(AlertType alertType) {
        try {
            Toolkit toolkit = Toolkit.getDefaultToolkit();
            
            // 根据报警类型播放不同次数的提示音
            int beepCount = alertType == AlertType.BUY_OPPORTUNITY ? 2 : 3;
            
            for (int i = 0; i < beepCount; i++) {
                toolkit.beep();
                if (i < beepCount - 1) {
                    Thread.sleep(200); // 间隔200ms
                }
            }
            
        } catch (Exception e) {
            logger.error("播放系统提示音失败: {}", e.getMessage());
        }
    }
    
    /**
     * 播放自定义音频文件
     * 支持WAV格式音频文件
     */
    private void playCustomSound(AlertType alertType) {
        try {
            String audioFileName = alertType == AlertType.BUY_OPPORTUNITY ?
                "up.wav" : "down.wav";
            
            // 尝试从resources目录加载音频文件
            InputStream audioStream = getClass().getClassLoader().getResourceAsStream("sounds/" + audioFileName);
            
            if (audioStream != null) {
                playWavFile(audioStream);
                logger.debug("播放自定义音频文件: {}", audioFileName);
            } else {
                // 尝试从外部文件加载
                File audioFile = new File("sounds/" + audioFileName);
                if (audioFile.exists()) {
                    playWavFile(audioFile);
                    logger.debug("播放外部音频文件: {}", audioFile.getAbsolutePath());
                } else {
                    logger.warn("未找到自定义音频文件: {}，使用系统提示音", audioFileName);
                    playSystemBeep(alertType);
                }
            }
            
        } catch (Exception e) {
            logger.error("播放自定义音频失败: {}，使用系统提示音", e.getMessage());
            playSystemBeep(alertType);
        }
    }
    
    /**
     * 播放多次提示音（强调重要机会）
     */
    private void playMultipleBeeps(AlertType alertType) {
        try {
            Toolkit toolkit = Toolkit.getDefaultToolkit();
            
            // 播放更多次数的提示音来强调
            int beepCount = alertType == AlertType.BUY_OPPORTUNITY ? 4 : 5;
            
            for (int i = 0; i < beepCount; i++) {
                toolkit.beep();
                Thread.sleep(150);
            }
            
        } catch (Exception e) {
            logger.error("播放多重提示音失败: {}", e.getMessage());
        }
    }
    
    /**
     * 播放WAV音频文件（从InputStream）
     */
    private void playWavFile(InputStream audioStream) throws Exception {
        try (AudioInputStream audioInputStream = AudioSystem.getAudioInputStream(audioStream)) {
            Clip clip = AudioSystem.getClip();
            clip.open(audioInputStream);
            clip.start();
            
            // 等待播放完成
            Thread.sleep(clip.getMicrosecondLength() / 1000);
            clip.close();
        }
    }
    
    /**
     * 播放WAV音频文件（从File）
     */
    private void playWavFile(File audioFile) throws Exception {
        try (AudioInputStream audioInputStream = AudioSystem.getAudioInputStream(audioFile)) {
            Clip clip = AudioSystem.getClip();
            clip.open(audioInputStream);
            clip.start();
            
            // 等待播放完成
            Thread.sleep(clip.getMicrosecondLength() / 1000);
            clip.close();
        }
    }
    
    /**
     * 报警类型枚举
     */
    public enum AlertType {
        BUY_OPPORTUNITY,    // 买入机会
        SELL_OPPORTUNITY    // 卖出机会
    }
    
    /**
     * 获取当前配置信息
     */
    public String getConfigInfo() {
        return String.format("报警配置 - 启用: %s, 买入阈值: $%.2f, 卖出阈值: $%.2f, 音频类型: %s",
            configService.isAlertEnabled(), configService.getBuyThreshold(), 
            configService.getSellThreshold(), configService.getSoundType());
    }
}
